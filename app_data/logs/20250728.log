2025-07-28 13:33:13,180 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:13,180 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversation")
2025-07-28 13:33:13,180 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user")
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("settings")
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,182 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("issuereport")
2025-07-28 13:33:13,182 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,182 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:33:13,187 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:13,187 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversation")
2025-07-28 13:33:13,187 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("settings")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("issuereport")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("API__index")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,189 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:33:13,227 - RAG.utils.hardware_validation - INFO - Hardware ID validated successfully.
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - Created 1 additional threads to maintain minimum of 1
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - MonitoredThreadPoolExecutor 'file_processing' initialized with min_workers=1, max_workers=2
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - Created 1 additional threads to maintain minimum of 1
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - MonitoredThreadPoolExecutor 'chat' initialized with min_workers=1, max_workers=2
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - ThreadPoolManager initialized with MonitoredThreadPoolExecutor
2025-07-28 13:33:13,229 - root - INFO - 初始化程序
2025-07-28 13:33:16,003 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-07-28 13:33:16,003 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: /home/<USER>/softwares/develop/rag/local_models/models--TencentBAC--Conan-embedding-v1
2025-07-28 13:33:16,003 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name /home/<USER>/softwares/develop/rag/local_models/models--TencentBAC--Conan-embedding-v1. Creating a new one with mean pooling.
2025-07-28 13:33:33,871 - RAG.reranking.baai_bge - INFO - 成功加载本地模型 '/home/<USER>/softwares/develop/rag/local_models/models--BAAI--bge-reranker-base
2025-07-28 13:33:42,682 - API.index.file.graph.pipelines - INFO - GraphRAG dependencies not installed. GraphRAG retriever pipeline will not work properly.
2025-07-28 13:33:42,683 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:42,688 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index" 
WHERE "API__index".name = ?
2025-07-28 13:33:42,688 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('File',)
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index" 
WHERE "API__index".name = ?
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - [cached since 0.001563s ago] ('GraphRAG',)
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:33:42,690 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:42,690 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index"
2025-07-28 13:33:42,690 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-28 13:33:42,690 - API.index.file.components - INFO - FileIndexHandler initialized for index 1
2025-07-28 13:33:42,756 - API.index.file.components - INFO - FileIndexHandler initialized for index 2
2025-07-28 13:33:42,765 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:33:42,765 - root - INFO - 完成初始化
2025-07-28 13:33:43,374 - API.queue.task_manager - INFO - TaskManager initialized with Redis client and ThreadPoolManager.
2025-07-28 13:33:43,374 - API.queue.workers.file_worker - INFO - FileWorker initialized with TaskManager
2025-07-28 13:33:43,374 - API.queue.thread_pools - INFO - Registered worker for task type: file
2025-07-28 13:33:43,374 - API.queue.workers.chat_worker - INFO - ChatWorker initialized with TaskManager and ChatService
2025-07-28 13:33:43,375 - API.queue.thread_pools - INFO - Registered worker for task type: chat
2025-07-28 13:33:44,385 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:33:44,385 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:34:45,441 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:34:45,441 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:35:46,503 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:35:46,503 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:36:10,185 - API.queue.task_manager - INFO - Creating task of type 'chat' with data: {'message': '可靠性要求都包括哪些？', 'user_id': 1, 'chat_history': [], 'conversation_id': None, 'conversation_name': None}
2025-07-28 13:36:10,186 - API.queue.task_manager - INFO - Created task 212c517e-fc7e-4f72-a235-c132dd2c81eb of type chat
2025-07-28 13:36:10,186 - API.queue.task_manager - INFO - Task 212c517e-fc7e-4f72-a235-c132dd2c81eb submitted to ThreadPoolManager.
2025-07-28 13:36:10,187 - API.queue.task_manager - INFO - Task 212c517e-fc7e-4f72-a235-c132dd2c81eb is pending in queue...
2025-07-28 13:36:10,187 - API.queue.task_manager - INFO - Registering worker for task 212c517e-fc7e-4f72-a235-c132dd2c81eb
2025-07-28 13:36:10,188 - API.queue.workers.chat_worker - INFO - ChatWorker - Processing task 212c517e-fc7e-4f72-a235-c132dd2c81eb
2025-07-28 13:36:10,190 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,192 - sqlalchemy.engine.Engine - INFO - INSERT INTO conversation (id, name, user, is_public, data_source, date_created, date_updated) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-07-28 13:36:10,192 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('1e3433f0bc87437486b7a0b80f34a8e7', '2025-07-28 05:36:10', 1, 0, '{}', '2025-07-28 05:36:10.188544', '2025-07-28 05:36:10.188545')
2025-07-28 13:36:10,193 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:36:10,286 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,287 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.id = ?
2025-07-28 13:36:10,287 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ('1e3433f0bc87437486b7a0b80f34a8e7',)
2025-07-28 13:36:10,288 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,289 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,290 - sqlalchemy.engine.Engine - INFO - SELECT user.id, user.username, user.username_lower, user.password, user.admin 
FROM user 
WHERE user.id = ?
2025-07-28 13:36:10,290 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] (1,)
2025-07-28 13:36:10,291 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,292 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,293 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.user = ? ORDER BY conversation.date_created DESC
2025-07-28 13:36:10,293 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] (1,)
2025-07-28 13:36:10,294 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,295 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,296 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.id = ?
2025-07-28 13:36:10,296 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ('1e3433f0bc87437486b7a0b80f34a8e7',)
2025-07-28 13:36:10,296 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,297 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,299 - sqlalchemy.engine.Engine - INFO - SELECT index__1__source.id 
FROM index__1__source
2025-07-28 13:36:10,299 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-28 13:36:10,300 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,303 - API.services.chat_service - INFO - 开始处理问题: '可靠性要求都包括哪些？'
2025-07-28 13:36:10,306 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,307 - sqlalchemy.engine.Engine - INFO - SELECT index__1__index.id, index__1__index.source_id, index__1__index.target_id, index__1__index.relation_type, index__1__index.user 
FROM index__1__index 
WHERE index__1__index.relation_type = ? AND index__1__index.source_id IN (?)
2025-07-28 13:36:10,307 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('document', '9aca0d2d-01f6-4db8-832b-879a1ee3dec4')
2025-07-28 13:36:10,308 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,310 - RAG.indices.vectorindex - INFO - VectorRetrieval text: 可靠性要求都包括哪些？, self.retrieval_mode: hybrid
2025-07-28 13:36:13,530 - RAG.indices.vectorindex - INFO - About to call query on <class 'RAG.storages.vectorstores.milvus.MilvusVectorStore'>
2025-07-28 13:36:25,812 - API.index.file.pipelines - INFO - 检索步骤耗时: 15.50374436378479
2025-07-28 13:36:25,821 - API.reasoning.simple - INFO - Got 0 retrieved documents
2025-07-28 13:36:25,823 - RAG.indices.rankings.llm - INFO - under rankings/llm/run: results_type: <class 'list'>, results: []
2025-07-28 13:36:25,827 - API.reasoning.simple - INFO - len (original): 0
2025-07-28 13:36:25,829 - API.reasoning.simple - INFO - 最后的evidence: 
2025-07-28 13:36:25,829 - API.reasoning.simple - INFO - Got 0 images
2025-07-28 13:36:25,829 - RAG.llms.chats.ollama - INFO - Streaming request to Ollama. Base URL: http://*************:10434/api, Endpoint: http://*************:10434/api/chat
2025-07-28 13:36:25,829 - RAG.llms.chats.ollama - INFO - Sending POST request to http://*************:10434/api/chat
2025-07-28 13:36:36,690 - RAG.llms.chats.ollama - INFO - Got response with status code: 200
2025-07-28 13:36:42,112 - RAG.llms.chats.ollama - INFO - Stream completed. Total chunks received: 421
2025-07-28 13:36:42,112 - RAG.llms.chats.ollama - INFO - Full content accumulated: '可靠性要求通常涵盖了一项产品、服务或系统在特定使用条件下持续满足性能需求的能力。它关注的是在预定的时间内，系统或部件能够无故障地执行其规定的功能而不会出现失败的概率。以下是可靠性要求的一些关键方面：

1. **设备寿命**：这是指产品的使用寿命，即预期的运行时间或周期，在此期间产品应能满足其设计的性能指标。

2. **可用性**：指的是在给定的时间段内，系统能够正常工作并满足用户需求的比例。这通常通过计算系统在某个特定时间段内处于工作状态的概率来衡量。

3. **故障率**：这是指单位时间内的故障发生频率，通常以每小时或每年的事件数来度量。降低故障率是提高可靠性的关键策略之一。

4. **维修性**：包括预防性维护、即时反应和纠正性维护的能力，以及在出现问题时恢复到正常运行状态的时间（MTTR）。

5. **可维护性和可操作性**：指的是用户或维护人员能够轻松地执行必要操作以保证系统性能的便利程度。这包括对用户界面的直观性、手册的质量、培训的需求等。

6. **环境适应性**：产品在不同环境下（如温度、湿度、振动）保持可靠运行的能力。

7. **安全要求**：除了可靠性之外，还考虑了防止意外故障导致伤害或损失的保护措施。这包括故障安全设计和应急程序。

8. **可预测性**：系统性能在未来特定时间点的行为能够被准确预测的程度。

9. **成本效益分析**：在保证可靠性的前提下，评估系统在全生命周期内的总拥有成本（TCO），考虑预防维护、故障后的修复费用以及潜在的生产停机损失等。

可靠性要求因产品类型和行业不同而有所差异，例如航空航天、汽车制造、电子设备等行业对于可靠性的需求可能非常严格。制定这些要求时通常需要综合考量产品的功能、使用环境、用户期望和服务成本等因素。'
2025-07-28 13:36:42,112 - RAG.llms.chats.ollama - INFO - Response done: True, Final result available: True
2025-07-28 13:36:42,112 - API.queue.workers.chat_worker - INFO - Chat task with 可靠性要求都包括哪些？ completed, marking as complete, time: 31.924553394317627
2025-07-28 13:36:42,112 - API.queue.task_manager - INFO - Task 212c517e-fc7e-4f72-a235-c132dd2c81eb status updated to 'completed'.
2025-07-28 13:36:47,564 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:36:47,564 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:37:48,591 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:37:48,591 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:38:49,652 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:38:49,652 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:39:50,655 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:39:50,655 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:40:51,697 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:40:51,697 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:41:52,717 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:41:52,717 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:42:53,730 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:42:53,730 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:43:54,792 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:43:54,792 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:44:55,853 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:44:55,853 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:45:56,914 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:45:56,914 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
