2025-07-28 13:33:13,180 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:13,180 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversation")
2025-07-28 13:33:13,180 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user")
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("settings")
2025-07-28 13:33:13,181 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,182 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("issuereport")
2025-07-28 13:33:13,182 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,182 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:33:13,187 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:13,187 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversation")
2025-07-28 13:33:13,187 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("settings")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("issuereport")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("API__index")
2025-07-28 13:33:13,188 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:33:13,189 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:33:13,227 - RAG.utils.hardware_validation - INFO - Hardware ID validated successfully.
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - Created 1 additional threads to maintain minimum of 1
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - MonitoredThreadPoolExecutor 'file_processing' initialized with min_workers=1, max_workers=2
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - Created 1 additional threads to maintain minimum of 1
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - MonitoredThreadPoolExecutor 'chat' initialized with min_workers=1, max_workers=2
2025-07-28 13:33:13,228 - API.queue.thread_pools - INFO - ThreadPoolManager initialized with MonitoredThreadPoolExecutor
2025-07-28 13:33:13,229 - root - INFO - 初始化程序
2025-07-28 13:33:16,003 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-07-28 13:33:16,003 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: /home/<USER>/softwares/develop/rag/local_models/models--TencentBAC--Conan-embedding-v1
2025-07-28 13:33:16,003 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name /home/<USER>/softwares/develop/rag/local_models/models--TencentBAC--Conan-embedding-v1. Creating a new one with mean pooling.
2025-07-28 13:33:33,871 - RAG.reranking.baai_bge - INFO - 成功加载本地模型 '/home/<USER>/softwares/develop/rag/local_models/models--BAAI--bge-reranker-base
2025-07-28 13:33:42,682 - API.index.file.graph.pipelines - INFO - GraphRAG dependencies not installed. GraphRAG retriever pipeline will not work properly.
2025-07-28 13:33:42,683 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:42,688 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index" 
WHERE "API__index".name = ?
2025-07-28 13:33:42,688 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('File',)
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index" 
WHERE "API__index".name = ?
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - [cached since 0.001563s ago] ('GraphRAG',)
2025-07-28 13:33:42,689 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:33:42,690 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:33:42,690 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index"
2025-07-28 13:33:42,690 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-28 13:33:42,690 - API.index.file.components - INFO - FileIndexHandler initialized for index 1
2025-07-28 13:33:42,756 - API.index.file.components - INFO - FileIndexHandler initialized for index 2
2025-07-28 13:33:42,765 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:33:42,765 - root - INFO - 完成初始化
2025-07-28 13:33:43,374 - API.queue.task_manager - INFO - TaskManager initialized with Redis client and ThreadPoolManager.
2025-07-28 13:33:43,374 - API.queue.workers.file_worker - INFO - FileWorker initialized with TaskManager
2025-07-28 13:33:43,374 - API.queue.thread_pools - INFO - Registered worker for task type: file
2025-07-28 13:33:43,374 - API.queue.workers.chat_worker - INFO - ChatWorker initialized with TaskManager and ChatService
2025-07-28 13:33:43,375 - API.queue.thread_pools - INFO - Registered worker for task type: chat
2025-07-28 13:33:44,385 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:33:44,385 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:34:45,441 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:34:45,441 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:35:46,503 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:35:46,503 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:36:10,185 - API.queue.task_manager - INFO - Creating task of type 'chat' with data: {'message': '可靠性要求都包括哪些？', 'user_id': 1, 'chat_history': [], 'conversation_id': None, 'conversation_name': None}
2025-07-28 13:36:10,186 - API.queue.task_manager - INFO - Created task 212c517e-fc7e-4f72-a235-c132dd2c81eb of type chat
2025-07-28 13:36:10,186 - API.queue.task_manager - INFO - Task 212c517e-fc7e-4f72-a235-c132dd2c81eb submitted to ThreadPoolManager.
2025-07-28 13:36:10,187 - API.queue.task_manager - INFO - Task 212c517e-fc7e-4f72-a235-c132dd2c81eb is pending in queue...
2025-07-28 13:36:10,187 - API.queue.task_manager - INFO - Registering worker for task 212c517e-fc7e-4f72-a235-c132dd2c81eb
2025-07-28 13:36:10,188 - API.queue.workers.chat_worker - INFO - ChatWorker - Processing task 212c517e-fc7e-4f72-a235-c132dd2c81eb
2025-07-28 13:36:10,190 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,192 - sqlalchemy.engine.Engine - INFO - INSERT INTO conversation (id, name, user, is_public, data_source, date_created, date_updated) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-07-28 13:36:10,192 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('1e3433f0bc87437486b7a0b80f34a8e7', '2025-07-28 05:36:10', 1, 0, '{}', '2025-07-28 05:36:10.188544', '2025-07-28 05:36:10.188545')
2025-07-28 13:36:10,193 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:36:10,286 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,287 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.id = ?
2025-07-28 13:36:10,287 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ('1e3433f0bc87437486b7a0b80f34a8e7',)
2025-07-28 13:36:10,288 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,289 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,290 - sqlalchemy.engine.Engine - INFO - SELECT user.id, user.username, user.username_lower, user.password, user.admin 
FROM user 
WHERE user.id = ?
2025-07-28 13:36:10,290 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] (1,)
2025-07-28 13:36:10,291 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,292 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,293 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.user = ? ORDER BY conversation.date_created DESC
2025-07-28 13:36:10,293 - sqlalchemy.engine.Engine - INFO - [generated in 0.00013s] (1,)
2025-07-28 13:36:10,294 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,295 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,296 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.id = ?
2025-07-28 13:36:10,296 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ('1e3433f0bc87437486b7a0b80f34a8e7',)
2025-07-28 13:36:10,296 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,297 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,299 - sqlalchemy.engine.Engine - INFO - SELECT index__1__source.id 
FROM index__1__source
2025-07-28 13:36:10,299 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ()
2025-07-28 13:36:10,300 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,303 - API.services.chat_service - INFO - 开始处理问题: '可靠性要求都包括哪些？'
2025-07-28 13:36:10,306 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:36:10,307 - sqlalchemy.engine.Engine - INFO - SELECT index__1__index.id, index__1__index.source_id, index__1__index.target_id, index__1__index.relation_type, index__1__index.user 
FROM index__1__index 
WHERE index__1__index.relation_type = ? AND index__1__index.source_id IN (?)
2025-07-28 13:36:10,307 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('document', '9aca0d2d-01f6-4db8-832b-879a1ee3dec4')
2025-07-28 13:36:10,308 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:36:10,310 - RAG.indices.vectorindex - INFO - VectorRetrieval text: 可靠性要求都包括哪些？, self.retrieval_mode: hybrid
2025-07-28 13:36:13,530 - RAG.indices.vectorindex - INFO - About to call query on <class 'RAG.storages.vectorstores.milvus.MilvusVectorStore'>
2025-07-28 13:36:25,812 - API.index.file.pipelines - INFO - 检索步骤耗时: 15.50374436378479
2025-07-28 13:36:25,821 - API.reasoning.simple - INFO - Got 0 retrieved documents
2025-07-28 13:36:25,823 - RAG.indices.rankings.llm - INFO - under rankings/llm/run: results_type: <class 'list'>, results: []
2025-07-28 13:36:25,827 - API.reasoning.simple - INFO - len (original): 0
2025-07-28 13:36:25,829 - API.reasoning.simple - INFO - 最后的evidence: 
2025-07-28 13:36:25,829 - API.reasoning.simple - INFO - Got 0 images
2025-07-28 13:36:25,829 - RAG.llms.chats.ollama - INFO - Streaming request to Ollama. Base URL: http://*************:10434/api, Endpoint: http://*************:10434/api/chat
2025-07-28 13:36:25,829 - RAG.llms.chats.ollama - INFO - Sending POST request to http://*************:10434/api/chat
2025-07-28 13:36:36,690 - RAG.llms.chats.ollama - INFO - Got response with status code: 200
2025-07-28 13:36:42,112 - RAG.llms.chats.ollama - INFO - Stream completed. Total chunks received: 421
2025-07-28 13:36:42,112 - RAG.llms.chats.ollama - INFO - Full content accumulated: '可靠性要求通常涵盖了一项产品、服务或系统在特定使用条件下持续满足性能需求的能力。它关注的是在预定的时间内，系统或部件能够无故障地执行其规定的功能而不会出现失败的概率。以下是可靠性要求的一些关键方面：

1. **设备寿命**：这是指产品的使用寿命，即预期的运行时间或周期，在此期间产品应能满足其设计的性能指标。

2. **可用性**：指的是在给定的时间段内，系统能够正常工作并满足用户需求的比例。这通常通过计算系统在某个特定时间段内处于工作状态的概率来衡量。

3. **故障率**：这是指单位时间内的故障发生频率，通常以每小时或每年的事件数来度量。降低故障率是提高可靠性的关键策略之一。

4. **维修性**：包括预防性维护、即时反应和纠正性维护的能力，以及在出现问题时恢复到正常运行状态的时间（MTTR）。

5. **可维护性和可操作性**：指的是用户或维护人员能够轻松地执行必要操作以保证系统性能的便利程度。这包括对用户界面的直观性、手册的质量、培训的需求等。

6. **环境适应性**：产品在不同环境下（如温度、湿度、振动）保持可靠运行的能力。

7. **安全要求**：除了可靠性之外，还考虑了防止意外故障导致伤害或损失的保护措施。这包括故障安全设计和应急程序。

8. **可预测性**：系统性能在未来特定时间点的行为能够被准确预测的程度。

9. **成本效益分析**：在保证可靠性的前提下，评估系统在全生命周期内的总拥有成本（TCO），考虑预防维护、故障后的修复费用以及潜在的生产停机损失等。

可靠性要求因产品类型和行业不同而有所差异，例如航空航天、汽车制造、电子设备等行业对于可靠性的需求可能非常严格。制定这些要求时通常需要综合考量产品的功能、使用环境、用户期望和服务成本等因素。'
2025-07-28 13:36:42,112 - RAG.llms.chats.ollama - INFO - Response done: True, Final result available: True
2025-07-28 13:36:42,112 - API.queue.workers.chat_worker - INFO - Chat task with 可靠性要求都包括哪些？ completed, marking as complete, time: 31.924553394317627
2025-07-28 13:36:42,112 - API.queue.task_manager - INFO - Task 212c517e-fc7e-4f72-a235-c132dd2c81eb status updated to 'completed'.
2025-07-28 13:36:47,564 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:36:47,564 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:37:48,591 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:37:48,591 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:38:49,652 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:38:49,652 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:39:50,655 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:39:50,655 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:40:51,697 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:40:51,697 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:41:52,717 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:41:52,717 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:42:53,730 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:42:53,730 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:43:54,792 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:43:54,792 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:44:55,853 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:44:55,853 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:45:56,914 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:45:56,914 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:46:57,975 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:46:57,975 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:47:58,984 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:47:58,984 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:49:00,045 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:49:00,045 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:50:01,049 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:50:01,049 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=31.93s
2025-07-28 13:50:45,632 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:50:45,632 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversation")
2025-07-28 13:50:45,632 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,633 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user")
2025-07-28 13:50:45,633 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,633 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("settings")
2025-07-28 13:50:45,633 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,634 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("issuereport")
2025-07-28 13:50:45,634 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,634 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:50:45,639 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:50:45,639 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversation")
2025-07-28 13:50:45,639 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user")
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("settings")
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("issuereport")
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("API__index")
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 13:50:45,640 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:50:45,678 - RAG.utils.hardware_validation - INFO - Hardware ID validated successfully.
2025-07-28 13:50:45,679 - API.queue.thread_pools - INFO - Created 1 additional threads to maintain minimum of 1
2025-07-28 13:50:45,679 - API.queue.thread_pools - INFO - MonitoredThreadPoolExecutor 'file_processing' initialized with min_workers=1, max_workers=2
2025-07-28 13:50:45,679 - API.queue.thread_pools - INFO - Created 1 additional threads to maintain minimum of 1
2025-07-28 13:50:45,679 - API.queue.thread_pools - INFO - MonitoredThreadPoolExecutor 'chat' initialized with min_workers=1, max_workers=2
2025-07-28 13:50:45,679 - API.queue.thread_pools - INFO - ThreadPoolManager initialized with MonitoredThreadPoolExecutor
2025-07-28 13:50:45,680 - root - INFO - 初始化程序
2025-07-28 13:50:48,198 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cuda
2025-07-28 13:50:48,198 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: /home/<USER>/softwares/develop/rag/local_models/models--TencentBAC--Conan-embedding-v1
2025-07-28 13:50:48,198 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name /home/<USER>/softwares/develop/rag/local_models/models--TencentBAC--Conan-embedding-v1. Creating a new one with mean pooling.
2025-07-28 13:50:50,740 - RAG.reranking.baai_bge - INFO - 成功加载本地模型 '/home/<USER>/softwares/develop/rag/local_models/models--BAAI--bge-reranker-base
2025-07-28 13:50:52,355 - API.index.file.graph.pipelines - INFO - GraphRAG dependencies not installed. GraphRAG retriever pipeline will not work properly.
2025-07-28 13:50:52,357 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:50:52,361 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index" 
WHERE "API__index".name = ?
2025-07-28 13:50:52,361 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] ('File',)
2025-07-28 13:50:52,362 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:50:52,362 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:50:52,362 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index" 
WHERE "API__index".name = ?
2025-07-28 13:50:52,362 - sqlalchemy.engine.Engine - INFO - [cached since 0.001587s ago] ('GraphRAG',)
2025-07-28 13:50:52,362 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:50:52,363 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:50:52,363 - sqlalchemy.engine.Engine - INFO - SELECT "API__index".id, "API__index".name, "API__index".index_type, "API__index".config 
FROM "API__index"
2025-07-28 13:50:52,363 - sqlalchemy.engine.Engine - INFO - [generated in 0.00010s] ()
2025-07-28 13:50:52,363 - API.index.file.components - INFO - FileIndexHandler initialized for index 1
2025-07-28 13:50:52,385 - API.index.file.components - INFO - FileIndexHandler initialized for index 2
2025-07-28 13:50:52,392 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:50:52,392 - root - INFO - 完成初始化
2025-07-28 13:50:52,721 - API.queue.task_manager - INFO - TaskManager initialized with Redis client and ThreadPoolManager.
2025-07-28 13:50:52,721 - API.queue.workers.file_worker - INFO - FileWorker initialized with TaskManager
2025-07-28 13:50:52,722 - API.queue.thread_pools - INFO - Registered worker for task type: file
2025-07-28 13:50:52,722 - API.queue.workers.chat_worker - INFO - ChatWorker initialized with TaskManager and ChatService
2025-07-28 13:50:52,722 - API.queue.thread_pools - INFO - Registered worker for task type: chat
2025-07-28 13:50:53,730 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:50:53,730 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:51:54,791 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:51:54,791 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:52:41,886 - API.queue.task_manager - INFO - Creating task of type 'chat' with data: {'message': '可靠性要求都包括哪些？', 'user_id': 1, 'chat_history': [], 'conversation_id': None, 'conversation_name': None}
2025-07-28 13:52:41,887 - API.queue.task_manager - INFO - Created task f99260d2-6e01-450c-b119-3e7048a172b7 of type chat
2025-07-28 13:52:41,887 - API.queue.task_manager - INFO - Task f99260d2-6e01-450c-b119-3e7048a172b7 submitted to ThreadPoolManager.
2025-07-28 13:52:41,888 - API.queue.task_manager - INFO - Registering worker for task f99260d2-6e01-450c-b119-3e7048a172b7
2025-07-28 13:52:41,888 - API.queue.workers.chat_worker - INFO - ChatWorker - Processing task f99260d2-6e01-450c-b119-3e7048a172b7
2025-07-28 13:52:41,893 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:41,895 - sqlalchemy.engine.Engine - INFO - INSERT INTO conversation (id, name, user, is_public, data_source, date_created, date_updated) VALUES (?, ?, ?, ?, ?, ?, ?)
2025-07-28 13:52:41,895 - sqlalchemy.engine.Engine - INFO - [generated in 0.00026s] ('0a0acce834ce45329b966c06b142b7ee', '2025-07-28 05:52:41', 1, 0, '{}', '2025-07-28 05:52:41.891766', '2025-07-28 05:52:41.891767')
2025-07-28 13:52:41,896 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 13:52:41,991 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:41,992 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.id = ?
2025-07-28 13:52:41,992 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('0a0acce834ce45329b966c06b142b7ee',)
2025-07-28 13:52:41,993 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:52:41,995 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:41,996 - sqlalchemy.engine.Engine - INFO - SELECT user.id, user.username, user.username_lower, user.password, user.admin 
FROM user 
WHERE user.id = ?
2025-07-28 13:52:41,996 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] (1,)
2025-07-28 13:52:41,996 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:52:41,998 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:41,998 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.user = ? ORDER BY conversation.date_created DESC
2025-07-28 13:52:41,998 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] (1,)
2025-07-28 13:52:41,999 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:52:42,001 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:42,001 - sqlalchemy.engine.Engine - INFO - SELECT conversation.id, conversation.name, conversation.user, conversation.is_public, conversation.data_source, conversation.date_created, conversation.date_updated 
FROM conversation 
WHERE conversation.id = ?
2025-07-28 13:52:42,001 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ('0a0acce834ce45329b966c06b142b7ee',)
2025-07-28 13:52:42,002 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:52:42,002 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:42,004 - sqlalchemy.engine.Engine - INFO - SELECT index__1__source.id 
FROM index__1__source
2025-07-28 13:52:42,004 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-28 13:52:42,005 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:52:42,008 - API.services.chat_service - INFO - 开始处理问题: '可靠性要求都包括哪些？'
2025-07-28 13:52:42,012 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 13:52:42,012 - sqlalchemy.engine.Engine - INFO - SELECT index__1__index.id, index__1__index.source_id, index__1__index.target_id, index__1__index.relation_type, index__1__index.user 
FROM index__1__index 
WHERE index__1__index.relation_type = ? AND index__1__index.source_id IN (?)
2025-07-28 13:52:42,013 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] ('document', '9aca0d2d-01f6-4db8-832b-879a1ee3dec4')
2025-07-28 13:52:42,013 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-28 13:52:42,015 - RAG.indices.vectorindex - INFO - VectorRetrieval text: 可靠性要求都包括哪些？, self.retrieval_mode: hybrid
2025-07-28 13:52:42,698 - RAG.indices.vectorindex - INFO - About to call query on <class 'RAG.storages.vectorstores.milvus.MilvusVectorStore'>
2025-07-28 13:52:46,087 - API.index.file.pipelines - INFO - 检索步骤耗时: 4.073819398880005
2025-07-28 13:52:46,096 - API.reasoning.simple - INFO - Got 0 retrieved documents
2025-07-28 13:52:46,098 - RAG.indices.rankings.llm - INFO - under rankings/llm/run: results_type: <class 'list'>, results: []
2025-07-28 13:52:46,102 - API.reasoning.simple - INFO - len (original): 0
2025-07-28 13:52:46,104 - API.reasoning.simple - INFO - 最后的evidence: 
2025-07-28 13:52:46,104 - API.reasoning.simple - INFO - Got 0 images
2025-07-28 13:52:46,104 - RAG.llms.chats.ollama - INFO - Streaming request to Ollama. Base URL: http://*************:10434/api, Endpoint: http://*************:10434/api/chat
2025-07-28 13:52:46,104 - RAG.llms.chats.ollama - INFO - Sending POST request to http://*************:10434/api/chat
2025-07-28 13:52:47,472 - RAG.llms.chats.ollama - INFO - Got response with status code: 200
2025-07-28 13:52:53,116 - RAG.llms.chats.ollama - INFO - Stream completed. Total chunks received: 439
2025-07-28 13:52:53,116 - RAG.llms.chats.ollama - INFO - Full content accumulated: '可靠性要求是确保产品或系统在预定的工作条件和预期的使用周期内，能稳定、准确地完成其指定功能的关键指标。它涉及到多个方面的评估和优化，以保证系统的长期运行性能与用户需求相匹配。下面是一些常见的可靠性要求类别：

1. **机械可靠性**：这是指设备或产品的物理结构在承受正常工作条件下的力、振动、温度变化等因素时的耐用性和稳定性。

2. **电气及电子可靠性**：涉及到电路、电子组件和系统在电能转换、传输和处理过程中的稳定性和效率，包括电源适应性、抗干扰能力等。

3. **软件可靠性**：关注的是软件系统的健壮性、容错能力和安全性。这包括错误的预防、检测和恢复机制，以确保软件在预期条件下能够正常运行而不会失败或产生不可预测的行为。

4. **环境适应性**：评估产品或系统在不同自然条件（如高温、低温、高湿度、极端温度变化等）下的性能和稳定性。

5. **安全可靠性**：涉及对潜在危险情况的预防和响应能力，包括紧急停止功能、故障隔离、过载保护等，确保人员和设备的安全。

6. **维护性和可操作性**：产品或系统的维修、保养以及用户操作的便利性。这包括易访问性、可替换部件的数量、用户界面的设计等。

7. **冗余与容错**：通过设计额外的组件或系统来提供备用功能，以在主要部分故障时仍能维持运行。

8. **生命周期成本评估**：考虑到设备或系统的整个生命周期中的所有成本，包括初始购置成本、维护和维修费用、能源消耗以及最终处置成本等。

9. **可追溯性和可维护性**：确保产品设计、制造和测试过程的记录完整，便于后续故障分析和维护。

每个具体应用领域（如航空航天、汽车工业、信息技术等）都有其特定的可靠性要求标准和评估方法。通常，通过使用概率论、统计学、模拟实验、实际测试等多种手段来量化和评估这些要求。'
2025-07-28 13:52:53,116 - RAG.llms.chats.ollama - INFO - Response done: True, Final result available: True
2025-07-28 13:52:53,116 - API.queue.workers.chat_worker - INFO - Chat task with 可靠性要求都包括哪些？ completed, marking as complete, time: 11.228799104690552
2025-07-28 13:52:53,117 - API.queue.task_manager - INFO - Task f99260d2-6e01-450c-b119-3e7048a172b7 status updated to 'completed'.
2025-07-28 13:52:55,839 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:52:55,839 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=11.23s
2025-07-28 13:53:56,872 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:53:56,872 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=11.23s
2025-07-28 13:54:57,934 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:54:57,934 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=11.23s
2025-07-28 13:55:58,995 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:55:58,996 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=11.23s
2025-07-28 13:57:00,052 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:57:00,052 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=11.23s
2025-07-28 13:58:01,104 - API.queue.thread_pools - INFO - Pool file status: active_threads=1, queue_size=0, completed_tasks=0, failed_tasks=0, avg_task_time=0.00s
2025-07-28 13:58:01,104 - API.queue.thread_pools - INFO - Pool chat status: active_threads=1, queue_size=0, completed_tasks=1, failed_tasks=0, avg_task_time=11.23s
