from RAG.utils.deploy_helper import DeploymentPathManager
DeploymentPathManager.init_package_paths()

from flowsettings import root_logger
import logging

import uvicorn
import torch
import logging
import signal
import sys
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.routing import APIRoute, Mount
from dotenv import load_dotenv

load_dotenv()

from theflow.settings import settings as flowsettings

# 获取模块的 logger
logger = logging.getLogger(__name__)

from API.main import App
from API.queue.thread_pools import get_thread_pool_manager
from RAG.utils.hardware_validation import validate_hardware

validate_hardware()

# 初始化线程池管理器
thread_pool_manager = get_thread_pool_manager()

# 设置信号处理函数
def signal_handler(sig, frame):
    logger.info("接收到中断信号，正在关闭应用...")
    if thread_pool_manager:
        logger.info("关闭线程池...")
        thread_pool_manager.shutdown(wait=False)
    logger.info("应用关闭完成。")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

app = FastAPI(debug=False)

# 从 flowsettings 中获取允许的前端来源
allow_origins = flowsettings.ALLOW_ORIGINS

# 使用简单的通用正则表达式，匹配192.168.3网段的所有IP和端口
allow_origin_regex = r"http://192\.168\.3\.\d+(?::\d+)?"
logger.debug(f"Allow origin regex: {allow_origin_regex}")

app.add_middleware(
    CORSMiddleware,
    # 移除 allow_origins，只使用正则表达式匹配，避免冲突
    # allow_origins=allow_origins,
    allow_origin_regex=allow_origin_regex,
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法，例如 GET、POST、OPTIONS
    allow_headers=["*"],  # 允许所有请求头
)

# 挂载静态文件目录，将`static`挂载到根路径`/`
app.mount("/static", StaticFiles(directory="static"), name="static")

# 实例化 App
rag_app = App()

# 注册路由
rag_app.register_routes(app)


# 添加根路径的路由
@app.get("/")
async def root():
    return RedirectResponse(url="/static/test.html")


# 处理 favicon 请求
@app.get("/favicon.ico")
async def favicon():
    return {}


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    # 捕捉 422 错误并输出详细信息
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": exc.body},
    )

# 打印注册的路由
for route in app.routes:
    if isinstance(route, APIRoute):
        logger.debug(f"API Route: {route.path}, methods: {route.methods}")
    elif isinstance(route, Mount):
        logger.debug(f"Mounted Route: {route.path}, name: {route.name}")
    else:
        logger.debug(f"Other Route: {route.path}, type: {type(route)}")

if __name__ == "__main__":
    try:
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=False, log_level="debug")  # 局域网内访问
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")
    finally:
        # 确保在退出时关闭线程池
        if thread_pool_manager:
            logger.info("关闭线程池...")
            thread_pool_manager.shutdown(wait=False)
        logger.info("应用已关闭。")

