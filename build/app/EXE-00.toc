('/home/<USER>/softwares/develop/rag/build/app/app',
 True,
 True,
 True,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 None,
 None,
 None,
 '/home/<USER>/softwares/develop/rag/build/app/app.pkg',
 [('pyi-bootloader-ignore-signals', '', 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/home/<USER>/softwares/develop/rag/build/app/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-310-x86_64-linux-gnu.so',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/lib-dynload/_struct.cpython-310-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-310-x86_64-linux-gnu.so',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/lib-dynload/zlib.cpython-310-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('struct',
   '/home/<USER>/softwares/develop/rag/build/app/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/home/<USER>/softwares/develop/rag/build/app/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/home/<USER>/softwares/develop/rag/build/app/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/home/<USER>/softwares/develop/rag/build/app/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('rthook-paddle',
   '/home/<USER>/softwares/develop/rag/hooks/rthook-paddle.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_traitlets',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_traitlets.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_nltk',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_nltk.py',
   'PYSOURCE'),
  ('app', '/home/<USER>/softwares/develop/rag/app.py', 'PYSOURCE')],
 [],
 False,
 False,
 1742452176,
 [('run_d',
   '/home/<USER>/softwares/miniforge3/envs/rag/lib/python3.10/site-packages/PyInstaller/bootloader/Linux-64bit-intel/run_d',
   'EXECUTABLE')],
 '/home/<USER>/softwares/miniforge3/envs/rag/lib/libpython3.10.so.1.0')
