from typing import Type, Optional

from theflow.settings import settings as flowsettings
from theflow.utils.modules import deserialize

from RAG.reranking.base import BaseReranking
from RAG.base import Param


class RerankingManager:
    """代表重排序模型池"""

    def __init__(self):
        self._models: dict[str, BaseReranking] = {}
        self._info: dict[str, dict] = {}
        self._default: str = ""
        self._vendors: list[Type] = []

        if hasattr(flowsettings, "RAG_RERANKINGS"):
            # 直接从设置中加载
            for name, model in flowsettings.RAG_RERANKINGS.items():
                # 动态传递模型参数（例如 model_name, local_model_path）
                # 先反序列化实例对象
                model_instance = deserialize(model["spec"], safe=False)

                # 从 spec 中提取参数，设置到 model_instance
                if hasattr(model_instance, 'model_name'):
                    model_instance.model_name = model["spec"].get("model_name")
                if hasattr(model_instance, 'local_model_path'):
                    model_instance.local_model_path = model["spec"].get("local_model_path")

                self._models[name] = model_instance
                self._info[name] = {
                    "name": name,
                    "spec": model["spec"],
                    "default": model.get("default", False)
                }
                if model.get("default", False):
                    self._default = name

            # 如果没有指定默认 reranker，则自动选择第一个作为默认值
            if not self._default and self._models:
                self._default = next(iter(self._models))

            self.load_vendors()

    def options(self) -> dict:
        """显示模型的字典"""
        return self._models

    def load_vendors(self):
        from RAG.reranking import CohereReranking, LocalBGEReranking

        self._vendors = [CohereReranking, LocalBGEReranking]
        # print(f"Loaded reranking vendors: {self._vendors}")

    def get_default_name(self) -> str:
        """获取默认模型的名称

        如果没有默认模型，则从池中选择随机模型。 如果有多个默认模型，则从中随机选择。

        返回:
            字符串: 模型名称
        """
        if not self._models:
            raise ValueError("没有可选模型")

        return self._default

    def __getitem__(self, key):
        """允许使用方括号访问模型"""
        if key == "default":
            if not self._default:
                raise KeyError("没有设置默认 reranker")
            return self._models[self._default]
        return self._models[key]


reranking_models_manager = RerankingManager()
