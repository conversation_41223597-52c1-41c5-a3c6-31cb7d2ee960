import datetime
import uuid
from typing import Optional
from zoneinfo import ZoneInfo

from sqlalchemy import JSON, Column
from sqlmodel import Field, SQLModel
from theflow.settings import settings as flowsettings


class BaseConversation(SQLModel):
    """存储用户和机器人之间的聊天对话

    属性：
        id: 用于标识对话的规范ID
        name: 对话的人类友好名称
        user: 用户ID
        data_source: 对话的数据源
        date_created: 对话创建的日期
        date_updated: 对话更新的日期
    """

    __table_args__ = {"extend_existing": True}

    id: str = Field(
        default_factory=lambda: uuid.uuid4().hex, primary_key=True, index=True
    )
    name: str = Field(
        default_factory=lambda: datetime.datetime.now(
            ZoneInfo(getattr(flowsettings, "TIME_ZONE", "UTC"))
        ).strftime("%Y-%m-%d %H:%M:%S")
    )
    user: int = Field(default=0)  # 目前我们只有一个用户

    is_public: bool = Field(default=False)

    # 包含消息和当前文件
    data_source: dict = Field(default={}, sa_column=Column(JSON))

    date_created: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    date_updated: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)


class BaseUser(SQLModel):
    """存储用户信息

    属性：
        id: 用于标识用户的规范ID
        username: 用户名
        password: 用户的哈希密码
    """

    __table_args__ = {"extend_existing": True}

    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(unique=True)
    username_lower: str = Field(unique=True)
    password: str
    admin: bool = Field(default=False)


class BaseSettings(SQLModel):
    """用户设置的记录

    属性：
        id: 用于标识设置的规范ID
        user: 用户ID
        setting: 用户设置（以字典/JSON格式存储）
    """

    __table_args__ = {"extend_existing": True}

    id: str = Field(
        default_factory=lambda: uuid.uuid4().hex, primary_key=True, index=True
    )
    user: int = Field(default=0)
    setting: dict = Field(default={}, sa_column=Column(JSON))


class BaseIssueReport(SQLModel):
    """存储用户报告的问题

    属性：
        id: 用于标识问题报告的规范ID
        issues: 用户报告的问题，格式为字典
        chat: 用户报告问题时的对话ID
        settings: 用户报告问题时的设置
        user: 用户ID
    """

    __table_args__ = {"extend_existing": True}

    id: Optional[int] = Field(default=None, primary_key=True)
    issues: dict = Field(default={}, sa_column=Column(JSON))
    chat: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    settings: Optional[dict] = Field(default=None, sa_column=Column(JSON))
    user: Optional[int] = Field(default=None)