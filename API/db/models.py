import API.db.base_models as base_models
from API.db.engine import sync_engine, async_engine
from sqlmodel import SQLModel
from theflow.settings import settings
from theflow.utils.modules import import_dotted_string
import asyncio

_base_conv = (
    import_dotted_string(settings.RAG_TABLE_CONV, safe=False)
    if hasattr(settings, "RAG_TABLE_CONV")
    else base_models.BaseConversation
)

_base_user = (
    import_dotted_string(settings.RAG_TABLE_USER, safe=False)
    if hasattr(settings, "RAG_TABLE_USER")
    else base_models.BaseUser
)

_base_settings = (
    import_dotted_string(settings.RAG_TABLE_SETTINGS, safe=False)
    if hasattr(settings, "RAG_TABLE_SETTINGS")
    else base_models.BaseSettings
)

_base_issue_report = (
    import_dotted_string(settings.RAG_TABLE_ISSUE_REPORT, safe=False)
    if hasattr(settings, "RAG_TABLE_ISSUE_REPORT")
    else base_models.BaseIssueReport
)


class Conversation(_base_conv, table=True):  # type: ignore
    """对话记录"""


class User(_base_user, table=True):  # type: ignore
    """用户表"""


class Settings(_base_settings, table=True):  # type: ignore
    """设置记录"""


class IssueReport(_base_issue_report, table=True):  # type: ignore
    """问题记录"""


async def create_tables():
    async with async_engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)


if not getattr(settings, "RAG_ENABLE_ALEMBIC", False):
    # SQLModel.metadata.create_all(engine)
    asyncio.run(create_tables())
