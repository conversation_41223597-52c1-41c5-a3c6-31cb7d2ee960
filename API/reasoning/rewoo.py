import html
import logging
from difflib import SequenceMatcher
from typing import AnyStr, Generator, Optional, Type

from API.llms.manager import llms
from API.reasoning.base import BaseReasoning
from API.utils.generator import Generator as GeneratorWrapper
from API.utils.render import Render
from langchain.text_splitter import CharacterTextSplitter
from pydantic import BaseModel, Field

from RAG.agents import (
    BaseTool,
    GoogleSearchTool,
    LLMTool,
    RewooAgent,
    WikipediaTool,
)
from RAG.base import BaseComponent, Document, HumanMessage, Node, SystemMessage
from RAG.llms import ChatLLM, PromptTemplate

from ..utils import SUPPORTED_LANGUAGE_MAP

logger = logging.getLogger(__name__)
DEFAULT_AGENT_STEPS = 4


DEFAULT_PLANNER_PROMPT = (
    "你是一个AI代理，在外部工具的帮助下逐步制定计划来解决问题。"
    "对于每一步，制定一个计划，然后调用一个工具，该工具将在稍后执行以检索该步骤的证据。"
    "你应该将每个证据存储到一个不同的变量 #E1, #E2, #E3 ... 中，这些变量可以在后续工具调用的输入中引用。\n\n"
    "##可用工具##\n"
    "{tool_description}\n\n"
    "##输出格式（替换 '<...>'）##\n"
    "#Plan1: <在这里描述你的计划>\n"
    "#E1: <工具名称>[<输入内容>] (例如. Search[什么是Python])\n"
    "#Plan2: <描述下一个计划>\n"
    "#E2: <工具名称>[<输入内容，你可以使用 #E1 来表示其预期输出>]\n"
    "等等...\n\n"
    "##你的任务##\n"
    "{task}\n\n"
    "##现在开始##\n"
)

DEFAULT_SOLVER_PROMPT = (
    "你是一个AI代理，在我（助手）的帮助下解决问题。我将提供逐步计划(#Plan)和证据(#E)，这些可能对你有帮助。"
    "你的任务是简要总结每一步，然后为你的任务做一个简短的最终结论。用 {lang} 给出答案。\n\n"
    "##我的计划和证据##\n"
    "{plan_evidence}\n\n"
    "##示例输出##\n"
    "首先，我 <做了某事> ，我认为 <...>；其次，我 <...>，我认为 <...>；....\n"
    "所以，<你的结论>。\n\n"
    "##你的任务##\n"
    "{task}\n\n"
    "##现在开始##\n"
)


class DocSearchArgs(BaseModel):
    query: str = Field(..., description="用于文档搜索的搜索查询")


class DocSearchTool(BaseTool):
    name: str = "docsearch"
    description: str = (
        "一个包含内部文档的存储。如果你缺乏回答问题所需的具体私有信息，可以在这个文档存储中搜索。"
        "此外，如果你不确定用户指的是哪个文档，很可能用户已经在文档存储中选择了目标文档，你只需要进行正常搜索。"
        "如果可能，尽量将搜索查询制定得尽可能具体。"
    )
    args_schema: Optional[Type[BaseModel]] = DocSearchArgs
    retrievers: list[BaseComponent] = []

    def _run_tool(self, query: AnyStr) -> AnyStr:
        docs = []
        doc_ids = []
        for retriever in self.retrievers:
            for doc in retriever(text=query):
                if doc.doc_id not in doc_ids:
                    docs.append(doc)
                    doc_ids.append(doc.doc_id)

        return self.prepare_evidence(docs)

    def prepare_evidence(self, docs, trim_len: int = 3000):
        evidence = ""
        table_found = 0

        for _id, retrieved_item in enumerate(docs):
            retrieved_content = ""
            page = retrieved_item.metadata.get("page_label", None)
            source = filename = retrieved_item.metadata.get("file_name", "-")
            if page:
                source += f" (Page {page})"
            if retrieved_item.metadata.get("type", "") == "table":
                if table_found < 5:
                    retrieved_content = retrieved_item.metadata.get("table_origin", "")
                    if retrieved_content not in evidence:
                        table_found += 1
                        evidence += (
                            f"<br><b>Table from {source}</b>\n"
                            + retrieved_content
                            + "\n<br>"
                        )
            elif retrieved_item.metadata.get("type", "") == "chatbot":
                retrieved_content = retrieved_item.metadata["window"]
                evidence += (
                    f"<br><b>Chatbot scenario from {filename} (Row {page})</b>\n"
                    + retrieved_content
                    + "\n<br>"
                )
            elif retrieved_item.metadata.get("type", "") == "image":
                retrieved_content = retrieved_item.metadata.get("image_origin", "")
                retrieved_caption = html.escape(retrieved_item.get_content())
                # PWS 不支持 VLM 图像，我们只存储标题
                evidence += (
                    f"<br><b>Figure from {source}</b>\n" + retrieved_caption + "\n<br>"
                )
            else:
                if "window" in retrieved_item.metadata:
                    retrieved_content = retrieved_item.metadata["window"]
                else:
                    retrieved_content = retrieved_item.text
                retrieved_content = retrieved_content.replace("\n", " ")
                if retrieved_content not in evidence:
                    evidence += (
                        f"<br><b>Content from {source}: </b> "
                        + retrieved_content
                        + " \n<br>"
                    )

            print("Retrieved #{}: {}".format(_id, retrieved_content))
            print("Score", retrieved_item.metadata.get("cohere_reranking_score", None))

        # 按 trim_len 修剪上下文
        if evidence:
            text_splitter = CharacterTextSplitter.from_tiktoken_encoder(
                chunk_size=trim_len,
                chunk_overlap=0,
                separator=" ",
                model_name="gpt-3.5-turbo",
            )
            texts = text_splitter.split_text(evidence)
            evidence = texts[0]

        return Document(content=evidence)


TOOL_REGISTRY = {
    "Google": GoogleSearchTool(),
    "Wikipedia": WikipediaTool(),
    "LLM": LLMTool(),
    "SearchDoc": DocSearchTool(),
}

DEFAULT_REWRITE_PROMPT = (
    "给定以下问题，重新表述并扩展它以帮助你更好地回答。"
    "保持原始问题中的所有信息。尽量保持问题简洁。"
    "用 {lang} 给出答案\n"
    "原始问题：{question}\n"
    "重新表述的问题："
)


class RewriteQuestionPipeline(BaseComponent):
    """重写用户问题

    参数:
        llm: 用于重写问题的语言模型
        rewrite_template: 用于llm重述文本输入的提示模板
        lang: 答案的语言。目前支持英语和日语
    """

    llm: ChatLLM = Node(default_callback=lambda _: llms.get_default())
    rewrite_template: str = DEFAULT_REWRITE_PROMPT

    lang: str = "Chinese"

    def run(self, question: str) -> Document:  # type: ignore
        prompt_template = PromptTemplate(self.rewrite_template)
        prompt = prompt_template.populate(question=question, lang=self.lang)
        messages = [
            SystemMessage(content="你是一个有帮助的助手"),
            HumanMessage(content=prompt),
        ]
        return self.llm(messages)


def find_text(llm_output, context):
    sentence_list = llm_output.split("\n")
    matches = []
    for sentence in sentence_list:
        match = SequenceMatcher(
            None, sentence, context, autojunk=False
        ).find_longest_match()
        matches.append((match.b, match.b + match.size))
    return matches

def find_text(llm_output, context):
    sentence_list = llm_output.split("\n")
    matches = []
    for sentence in sentence_list:
        match = SequenceMatcher(
            None, sentence, context, autojunk=False
        ).find_longest_match()
        matches.append((match.b, match.b + match.size))
    return matches


class RewooAgentPipeline(BaseReasoning):
    """使用 ReWOO 代理的问答管道。"""

    class Config:
        allow_extra = True

    retrievers: list[BaseComponent]
    agent: RewooAgent = RewooAgent.withx()
    rewrite_pipeline: RewriteQuestionPipeline = RewriteQuestionPipeline.withx()
    use_rewrite: bool = False
    enable_citation: bool = False

    def format_info_panel_evidence(self, worker_log):
        header = ""
        content = []

        for line in worker_log.splitlines():
            if line.startswith("#Plan"):
                # 以 #Plan 开头的行应标记为新段落
                header = line
            elif line.startswith("#Action"):
                # 对 markdown 输出进行小修复
                line = "\\" + line + "<br>"
                content.append(line)
            elif line.startswith("#"):
                # 阻止 markdown 渲染大标题
                line = "\\" + line
                content.append(line)
            else:
                content.append(line)

        if not header:
            return

        return Document(
            channel="info",
            content=Render.collapsible(
                header=header,
                content=Render.table("\n".join(content)),
                open=False,
            ),
        )

    def format_info_panel_planner(self, planner_output):
        planner_output = planner_output.replace("\n", "<br>")
        return Document(
            channel="info",
            content=Render.collapsible(
                header="Planner Output",
                content=planner_output,
                open=True,
            ),
        )

    def prepare_citation(self, answer) -> list[Document]:
        """准备要在UI上显示的引用"""
        segments = []
        split_indices = [
            0,
        ]
        start_indices = set()
        text = ""

        if "citation" in answer.metadata and answer.metadata["citation"] is not None:
            context = answer.metadata["worker_log"]
            for fact_with_evidence in answer.metadata["citation"].answer:
                for quote in fact_with_evidence.substring_quote:
                    matches = find_text(quote, context)
                    for match in matches:
                        split_indices.append(match[0])
                        split_indices.append(match[1])
                        start_indices.add(match[0])
            split_indices = sorted(list(set(split_indices)))
            spans = []
            prev = 0
            for index in split_indices:
                if index > prev:
                    spans.append(context[prev:index])
                    prev = index
            spans.append(context[split_indices[-1] :])

            prev = 0
            for span, start_idx in list(zip(spans, split_indices)):
                if start_idx in start_indices:
                    text += Render.highlight(span)
                else:
                    text += span

        else:
            text = answer.metadata["worker_log"]

        # 通过检测标题 #Plan 来分隔文本
        for line in text.splitlines():
            if line.startswith("#Plan"):
                # 以 #Plan 开头的行应标记为新段落
                new_segment = [line]
                segments.append(new_segment)
            elif line.startswith("#Action"):
                # 对 markdown 输出进行小修复
                line = "\\" + line + "<br>"
                segments[-1].append(line)
            elif line.startswith("#"):
                # 阻止 markdown 渲染大标题
                line = "\\" + line
                segments[-1].append(line)
            else:
                if segments:
                    segments[-1].append(line)
                else:
                    segments.append([line])

        outputs = []
        for segment in segments:
            outputs.append(
                Document(
                    channel="info",
                    content=Render.collapsible(
                        header=segment[0],
                        content=Render.table("\n".join(segment[1:])),
                        open=True,
                    ),
                )
            )

        return outputs

    async def ainvoke(  # type: ignore
        self, message, conv_id: str, history: list, **kwargs  # type: ignore
    ) -> Document:
        answer = self.agent(message, use_citation=True)
        self.report_output(Document(content=answer.text, channel="chat"))

        refined_citations = self.prepare_citation(answer)
        for _ in refined_citations:
            self.report_output(_)

        self.report_output(None)
        return answer

    def stream(  # type: ignore
        self, message, conv_id: str, history: list, **kwargs  # type: ignore
    ) -> Generator[Document, None, Document] | None:
        if self.use_rewrite:
            rewrite = self.rewrite_pipeline(question=message)
            message = rewrite.text
            yield Document(
                channel="info",
                content=f"将消息重写为: {rewrite.text}",
            )

        output_stream = GeneratorWrapper(
            self.agent.stream(message, use_citation=self.enable_citation)
        )
        for item in output_stream:
            if item.intermediate_steps:
                for step in item.intermediate_steps:
                    if "planner_log" in step:
                        yield Document(
                            channel="info",
                            content=self.format_info_panel_planner(step["planner_log"]),
                        )
                    else:
                        yield Document(
                            channel="info",
                            content=self.format_info_panel_evidence(step["worker_log"]),
                        )
            if item.text:
                # 最终答案
                yield Document(channel="chat", content=item.text)

        answer = output_stream.value
        yield Document(channel="info", content=None)
        yield from self.prepare_citation(answer)

        return answer

    @classmethod
    def get_pipeline(
        cls, settings: dict, states: dict, retrievers: list | None = None
    ) -> BaseReasoning:
        _id = cls.get_info()["id"]
        prefix = f"reasoning.options.{_id}"
        pipeline = RewooAgentPipeline(retrievers=retrievers)

        max_context_length_setting = settings.get("reasoning.max_context_length", None)

        planner_llm_name = settings[f"{prefix}.planner_llm"]
        planner_llm = llms.get(planner_llm_name, llms.get_default())
        solver_llm_name = settings[f"{prefix}.solver_llm"]
        solver_llm = llms.get(solver_llm_name, llms.get_default())

        pipeline.agent.planner_llm = planner_llm
        pipeline.agent.solver_llm = solver_llm
        if max_context_length_setting:
            pipeline.agent.max_context_length = (
                max_context_length_setting // DEFAULT_AGENT_STEPS
            )

        tools = []
        for tool_name in settings[f"{prefix}.tools"]:
            tool = TOOL_REGISTRY[tool_name]
            if tool_name == "SearchDoc":
                tool.retrievers = retrievers
            elif tool_name == "LLM":
                tool.llm = solver_llm
            tools.append(tool)
        pipeline.agent.plugins = tools
        pipeline.agent.output_lang = SUPPORTED_LANGUAGE_MAP.get(
            settings["reasoning.lang"], "Chinese"
        )
        pipeline.agent.prompt_template["Planner"] = PromptTemplate(
            settings[f"{prefix}.planner_prompt"]
        )
        pipeline.agent.prompt_template["Solver"] = PromptTemplate(
            settings[f"{prefix}.solver_prompt"]
        )

        pipeline.enable_citation = settings[f"{prefix}.highlight_citation"]
        pipeline.use_rewrite = states.get("app", {}).get("regen", False)
        pipeline.rewrite_pipeline.llm = (
            planner_llm  # TODO: 如果需要，可以为重写单独设置llm
        )

        return pipeline

    @classmethod
    def get_user_settings(cls) -> dict:

        llm = ""
        llm_choices = [("(default)", "")]
        try:
            llm_choices += [(_, _) for _ in llms.options().keys()]
        except Exception as e:
            logger.exception(f"Failed to get LLM options: {e}")

        tool_choices = ["Wikipedia", "Google", "LLM", "SearchDoc"]

        return {
            "planner_llm": {
                "name": "Planner 的语言模型",
                "value": llm,
                "component": "dropdown",
                "choices": llm_choices,
                "special_type": "llm",
                "info": (
                    "用于规划的语言模型。"
                    "该模型将根据指令生成计划以找到答案。"
                ),
            },
            "solver_llm": {
                "name": "Solver 的语言模型",
                "value": llm,
                "component": "dropdown",
                "choices": llm_choices,
                "special_type": "llm",
                "info": (
                    "用于解决的语言模型。"
                    "该模型将根据 Planner 生成的计划和工具找到的证据生成答案。"
                ),
            },
            "highlight_citation": {
                "name": "高亮引用",
                "value": False,
                "component": "checkbox",
            },
            "tools": {
                "name": "知识检索工具",
                "value": ["SearchDoc", "LLM"],
                "component": "checkboxgroup",
                "choices": tool_choices,
            },
            "planner_prompt": {
                "name": "Planner 提示",
                "value": DEFAULT_PLANNER_PROMPT,
            },
            "solver_prompt": {
                "name": "Solver 提示",
                "value": DEFAULT_SOLVER_PROMPT,
            },
        }

    @classmethod
    def get_info(cls) -> dict:
        return {
            "id": "ReWOO",
            "name": "ReWOO 代理",
            "description": (
                "实现 ReWOO 范式：https://arxiv.org/abs/2305.18323。"
                "ReWOO 代理在第一阶段制定逐步计划，然后在第二阶段解决每个步骤。"
                "代理可以使用外部工具来帮助推理过程。一旦所有阶段完成，代理将总结答案。"
            ),
        }
