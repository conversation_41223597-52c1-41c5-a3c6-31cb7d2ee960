from typing import Optional

from RAG.base import BaseComponent


class BaseReasoning(BaseComponent):
    """处理每个用户聊天消息的推理管道

    这个推理管道可以访问：
        - 检索器
        - 用户设置
        - 消息
        - 对话ID
        - 消息历史
    """

    @classmethod
    def get_info(cls) -> dict:
        """获取管道信息，以便应用程序组织和显示

        返回:
            包含以下键的字典:
                - "id": 管道的唯一ID
                - "name": 管道的人类友好名称
                - "description": 管道的概述简短描述，用于让用户了解管道的作用
        """
        raise NotImplementedError

    @classmethod
    def get_user_settings(cls) -> dict:
        """获取此管道的默认用户设置"""
        return {}

    @classmethod
    def get_pipeline(
        cls,
        user_settings: dict,
        state: dict,
        retrievers: Optional[list["BaseComponent"]] = None,
    ) -> "BaseReasoning":
        """获取推理管道以供应用程序执行

        参数:
            user_setting: 用户设置
            state: 对话状态
            retrievers (list): 检索器列表
        """
        return cls()

    def run(self, message: str, conv_id: str, history: list, **kwargs):  # type: ignore
        """执行推理管道"""
        raise NotImplementedError