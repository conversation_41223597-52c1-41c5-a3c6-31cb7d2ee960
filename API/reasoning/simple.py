import html
import logging
import threading
import asyncio
from sqlmodel import Session
from sqlalchemy.future import select
from collections import defaultdict
from difflib import SequenceMatcher
from functools import partial
from typing import Generator, ClassVar
from theflow.base import unset_

import numpy as np
import tiktoken
from API.llms.manager import llms
from API.reasoning.prompt_optimization import (
    DecomposeQuestionPipeline,
    RewriteQuestionPipeline,
)
from API.utils.render import Render
from API.db.engine import sync_engine, async_engine
from API.index.file.pipelines import DocumentRetrievalPipeline
from theflow.settings import settings as flowsettings

from RAG.base import (
    AIMessage,
    BaseComponent,
    Document,
    HumanMessage,
    Node,
    RetrievedDocument,
    SystemMessage,
)
from RAG.indices.qa.citation import CitationPipeline
from RAG.indices.splitters import TokenSplitter, RecursiveCharacterSplitter
from RAG.llms import ChatLLM, PromptTemplate

from ..utils import SUPPORTED_LANGUAGE_MAP
from .base import BaseReasoning

logger = logging.getLogger(__name__)

EVIDENCE_MODE_TEXT = 0
EVIDENCE_MODE_TABLE = 1
EVIDENCE_MODE_CHATBOT = 2
EVIDENCE_MODE_FIGURE = 3
MAX_IMAGES = 10
CITATION_TIMEOUT = 5.0


def find_text(search_span, context):
    sentence_list = search_span.split("\n")
    context = context.replace("\n", " ")

    matches = []
    # 不要搜索太短的文本
    if len(search_span) > 5:
        for sentence in sentence_list:
            match = SequenceMatcher(
                None, sentence, context, autojunk=False
            ).find_longest_match()
            if match.size > max(len(sentence) * 0.35, 5):
                matches.append((match.b, match.b + match.size))

    return matches


class PrepareEvidencePipeline(BaseComponent):
    """从检索到的文档列表中准备证据文本

    这一步通常发生在 `DocumentRetrievalPipeline` 之后。

    参数:
        trim_func: 一个回调函数或 BaseComponent，用于将大块文本分割成更小的部分。第一个部分将被保留。
    """

    max_context_length: int = 32000
    # trim_func: TokenSplitter | None = None
    trim_func: RecursiveCharacterSplitter | None = None

    def run(self, docs: list[RetrievedDocument]) -> Document:
        evidence = ""
        images = []
        table_found = 0
        evidence_modes = []

        evidence_trim_func = (
            self.trim_func
            if self.trim_func
            else RecursiveCharacterSplitter( # TokenSplitter
                chunk_size=self.max_context_length,
                chunk_overlap=0,
                # separator=" ",
                # tokenizer=partial(
                #     tiktoken.encoding_for_model("gpt-3.5-turbo").encode,
                #     allowed_special=set(),
                #     disallowed_special="all",
                # ),
            )
        )

        for _id, retrieved_item in enumerate(docs):
            retrieved_content = ""
            page = retrieved_item.metadata.get("page_label", None)
            source = filename = retrieved_item.metadata.get("file_name", "-")
            if page:
                source += f" (Page {page})"
            if retrieved_item.metadata.get("type", "") == "table":
                evidence_modes.append(EVIDENCE_MODE_TABLE)
                if table_found < 5:
                    retrieved_content = retrieved_item.metadata.get(
                        "table_origin", retrieved_item.text
                    )
                    if retrieved_content not in evidence:
                        table_found += 1
                        evidence += (
                            f"<br><b>Table from {source}</b>\n"
                            + retrieved_content
                            + "\n<br>"
                        )
            elif retrieved_item.metadata.get("type", "") == "chatbot":
                evidence_modes.append(EVIDENCE_MODE_CHATBOT)
                retrieved_content = retrieved_item.metadata["window"]
                evidence += (
                    f"<br><b>Chatbot scenario from {filename} (Row {page})</b>\n"
                    + retrieved_content
                    + "\n<br>"
                )
            elif retrieved_item.metadata.get("type", "") == "image":
                evidence_modes.append(EVIDENCE_MODE_FIGURE)
                retrieved_content = retrieved_item.metadata.get("image_origin", "")
                retrieved_caption = html.escape(retrieved_item.get_content())
                evidence += (
                    f"<br><b>Figure from {source}</b>\n"
                    + "<img width='85%' src='<src>' "
                    + f"alt='{retrieved_caption}'/>"
                    + "\n<br>"
                )
                images.append(retrieved_content)
            else:
                if "window" in retrieved_item.metadata:
                    retrieved_content = retrieved_item.metadata["window"]
                else:
                    retrieved_content = retrieved_item.text
                retrieved_content = retrieved_content.replace("\n", " ")
                if retrieved_content not in evidence:
                    evidence += (
                        f"<br><b>Content from {source}: </b> "
                        + retrieved_content
                        + " \n<br>"
                    )

        # 解析证据模式
        evidence_mode = EVIDENCE_MODE_TEXT
        if EVIDENCE_MODE_FIGURE in evidence_modes:
            evidence_mode = EVIDENCE_MODE_FIGURE
        elif EVIDENCE_MODE_TABLE in evidence_modes:
            evidence_mode = EVIDENCE_MODE_TABLE

        # 按 trim_len 修剪上下文
        logger.info(f"len (original): {len(evidence)}")
        if evidence:
            texts = evidence_trim_func([Document(text=evidence)])
            evidence = texts[0].text
            logger.info(f"len (trimmed): {len(evidence)}")

        return Document(content=(evidence_mode, evidence, images))


DEFAULT_QA_TEXT_PROMPT = (
    "使用以下上下文片段详细回答末尾的问题，并给出清晰的解释。"
    "如果你不知道答案，只需说你不知道，不要试图编造答案。"
    "用 {lang} 给出答案。\n\n"
    "{context}\n"
    "问题: {question}\n"
    "有帮助的答案:"
)

DEFAULT_QA_TABLE_PROMPT = (
    "使用给定的上下文：文本、表格和图形来回答问题，然后提供清晰的解释。"
    "如果有上下文，尽量遵循上下文的内容，不要随意发挥太多。"
    "如果你不知道答案，只需说你不知道，不要试图编造答案。"
    "用 {lang} 给出答案。\n\n"
    "上下文:\n"
    "{context}\n"
    "问题: {question}\n"
    "有帮助的答案:"
)

DEFAULT_QA_CHATBOT_PROMPT = (
    "选择最合适的聊天机器人场景来回答末尾的问题，输出提供的答案文本。"
    "如果你不知道答案，只需说你不知道。保持答案尽可能简洁。"
    "用 {lang} 给出答案。\n\n"
    "上下文:\n"
    "{context}\n"
    "问题: {question}\n"
    "答案:"
)

DEFAULT_QA_FIGURE_PROMPT = (
    "使用给定的上下文：文本、表格和图形来回答问题。"
    "如果你不知道答案，只需说你不知道。"
    "用 {lang} 给出答案。\n\n"
    "上下文: \n"
    "{context}\n"
    "问题: {question}\n"
    "答案: "
)

CONTEXT_RELEVANT_WARNING_SCORE = 0.7


class AnswerWithContextPipeline(BaseComponent):
    """基于证据回答问题

    参数:
        llm: 用于生成答案的语言模型
        citation_pipeline: 从证据生成引用的管道
        qa_template: 用于LLM生成答案的提示模板（参考 evidence_mode）
        qa_table_template: 用于LLM生成表格答案的提示模板（参考 evidence_mode）
        qa_chatbot_template: 用于LLM生成预制场景答案的提示模板（参考 evidence_mode）
        lang: 答案的语言。目前支持英语和日语
    """

    llm: ChatLLM = Node(default_callback=lambda _: llms.get_default())
    vlm_endpoint: str = getattr(flowsettings, "RAG_VLM_ENDPOINT", "")
    use_multimodal: bool = getattr(flowsettings, "RAG_REASONINGS_USE_MULTIMODAL", True)
    citation_pipeline: CitationPipeline = Node(
        default_callback=lambda _: CitationPipeline(llm=llms.get_default())
    )

    qa_template: str = DEFAULT_QA_TEXT_PROMPT
    qa_table_template: str = DEFAULT_QA_TABLE_PROMPT
    qa_chatbot_template: str = DEFAULT_QA_CHATBOT_PROMPT
    qa_figure_template: str = DEFAULT_QA_FIGURE_PROMPT

    enable_citation: bool = False
    system_prompt: str = ""
    lang: str = "Chinese"  # 支持中文和英语
    n_last_interactions: int = 5

    def get_prompt(self, question, evidence, evidence_mode: int):
        """为LLM准备提示和其他信息"""
        if evidence_mode == EVIDENCE_MODE_TEXT:
            prompt_template = PromptTemplate(self.qa_template)
        elif evidence_mode == EVIDENCE_MODE_TABLE:
            prompt_template = PromptTemplate(self.qa_table_template)
        elif evidence_mode == EVIDENCE_MODE_FIGURE:
            if self.use_multimodal:
                prompt_template = PromptTemplate(self.qa_figure_template)
            else:
                prompt_template = PromptTemplate(self.qa_template)
        else:
            prompt_template = PromptTemplate(self.qa_chatbot_template)

        prompt = prompt_template.populate(
            context=evidence,
            question=question,
            lang=self.lang,
        )

        return prompt, evidence

    def run(
        self, question: str, evidence: str, evidence_mode: int = 0, **kwargs
    ) -> Document:
        return self.invoke(question, evidence, evidence_mode, **kwargs)

    def invoke(
        self,
        question: str,
        evidence: str,
        evidence_mode: int = 0,
        images: list[str] = [],
        **kwargs,
    ) -> Document:
        raise NotImplementedError

    async def ainvoke(  # type: ignore
        self,
        question: str,
        evidence: str,
        evidence_mode: int = 0,
        images: list[str] = [],
        **kwargs,
    ) -> Document:
        """基于证据回答问题

        除了问题和证据，这个方法还考虑了 evidence_mode。evidence_mode 告诉证据的类型。
        证据的类型影响：
            1. 证据的表示方式。
            2. 生成答案的提示。

        默认情况下，evidence_mode 为 0，这意味着证据是纯文本，没有特定的语义表示。evidence_mode 可以是：
            1. "table": 会有 HTML 标记表明证据中有一个表格。
            2. "chatbot": 会有 HTML 标记表明证据中有一个聊天机器人。这个聊天机器人是一个场景，从 Excel 文件中提取，其中每一行对应一个交互。

        参数:
            question: 用户提出的原始问题
            evidence: 包含回答问题相关信息的文本（由检索管道确定）
            evidence_mode: 证据的模式，0 表示文本，1 表示表格，2 表示聊天机器人
        """
        raise NotImplementedError

    def stream(  # type: ignore
        self,
        question: str,
        evidence: str,
        evidence_mode: int = 0,
        images: list[str] = [],
        **kwargs,
    ) -> Generator[Document, None, Document]:
        history = kwargs.get("history", [])
        logger.info(f"Got {len(images)} images")
        # 检查证据是否存在，使用 QA 提示
        if evidence:
            prompt, evidence = self.get_prompt(question, evidence, evidence_mode)
        else:
            prompt = question

        # 检索引用
        citation = None

        def citation_call():
            nonlocal citation
            citation = self.citation_pipeline(context=evidence, question=question)

        if evidence and self.enable_citation:
            # 在线程中执行函数调用
            citation_thread = threading.Thread(target=citation_call)
            citation_thread.start()
        else:
            citation_thread = None

        output = ""
        accumulated_text = ""  # 用于保存完整回答
        logprobs = []

        messages = []
        if self.system_prompt:
            messages.append(SystemMessage(content=self.system_prompt))
        for human, ai in history[-self.n_last_interactions :]:
            messages.append(HumanMessage(content=human))
            messages.append(AIMessage(content=ai))

        if self.use_multimodal and evidence_mode == EVIDENCE_MODE_FIGURE:
            # 创建图像消息:
            messages.append(
                HumanMessage(
                    content=[
                        {"type": "text", "text": prompt},
                    ]
                    + [
                        {
                            "type": "image_url",
                            "image_url": {"url": image},
                        }
                        for image in images[:MAX_IMAGES]
                    ],
                )
            )
        else:
            # 追加主提示
            messages.append(HumanMessage(content=prompt))

        try:
            # 首先尝试流式处理
            # 在调用 LLM.stream 之前
            logger.debug(f"LLM type: {type(self.llm)}")
            logger.debug(f"LLM stream method type: {type(self.llm.stream)}")
            logger.debug("Trying LLM streaming")
            
            # 跟踪是否至少收到一个响应
            received_any_response = False
            
            # 追踪已经生成的文本，避免重复
            for out_msg in self.llm.stream(messages):
                received_any_response = True
                # logger.debug(f"out_msg type: {type(out_msg)}, out_msg: {out_msg}")
                
                # 获取当前块的文本内容
                chunk_text = out_msg.text or ""  # 确保即使text为None也能处理
                
                # 如果块中有内容
                if chunk_text:
                    # 更新完整文本
                    accumulated_text += chunk_text
                    output += chunk_text
                    logprobs += out_msg.logprobs
                    
                    # 创建并yield文档对象
                    response_doc = Document(channel="chat", content=chunk_text)
                    # logger.debug(f"生成响应块: '{chunk_text}', 当前累积: '{accumulated_text}'")
                    # logger.debug(f"Debug under simple / AnswerWithContextPipeline: response_doc type: {type(response_doc)}, response_doc: {response_doc}")
                    # logger.debug(f"content: {response_doc.content}")
                    yield response_doc.text
            
            # 如果没有产生任何输出但接收到了响应，尝试使用accumulated_text
            if received_any_response and not output and accumulated_text:
                logger.warning(f"没有生成任何单独的输出块，但累积了完整响应: '{accumulated_text}'")
                doc = Document(channel="chat", content=accumulated_text)
                yield doc.text
                output = accumulated_text
                
        except Exception as e:
            logger.info(f"流式处理时出错: {e}")
            logger.info("Streaming is not supported, falling back to normal processing")
            try:
                output = self.llm(messages).text
                if output:
                    yield output
                else:
                    yield "模型未返回有效回答"
            except Exception as invoke_error:
                logger.error(f"非流式调用也失败: {invoke_error}")
                yield f"调用模型时发生错误: {str(invoke_error)}"

        # 如果最终输出仍为空但模型已经处理了请求，提供默认响应
        if not output and received_any_response:
            default_msg = "很抱歉，模型没有生成有效的回答。"
            logger.warning(f"模型未产生有效输出，使用默认回答: {default_msg}")
            yield default_msg
            output = default_msg

        if logprobs:
            qa_score = np.exp(np.average(logprobs))
        else:
            qa_score = None

        if citation_thread:
            citation_thread.join(timeout=CITATION_TIMEOUT)

        answer = Document(
            text=output,
            metadata={"citation": citation, "qa_score": qa_score},
        )

        logger.debug(f"最终回答: {answer}, 类型: {type(answer)}")
        return answer


class AddQueryContextPipeline(BaseComponent):

    n_last_interactions: int = 5
    llm: ChatLLM = Node(default_callback=lambda _: llms.get_default())

    def run(self, question: str, history: list) -> Document:
        messages = [
            SystemMessage(
                content="以下是到目前为止的对话历史记录，以及用户提出的需要通过搜索知识库来回答的新问题。\n"
                "你可以访问一个包含数百个文档的搜索索引。\n"
                "根据对话和新问题生成一个搜索查询。\n"
                "不要在搜索查询术语中包含引用的源文件名和文档名，例如 info.txt 或 doc.pdf。\n"
                "不要在搜索查询术语中包含 [] 或 <<>> 内的任何文本。\n"
                "不要包含任何特殊字符，如 '+'。\n"
                "如果问题不是英文的，请用问题中使用的语言重写查询。\n"
                "如果问题包含足够的信息，只需返回数字 1。\n"
                "如果不需要进行搜索，只需返回数字 0。"
            ),
            HumanMessage(content="How did crypto do last year?"),
            AIMessage(
                content="Summarize Cryptocurrency Market Dynamics from last year"
            ),
            HumanMessage(content="What are my health plans?"),
            AIMessage(content="Show available health plans"),
        ]
        for human, ai in history[-self.n_last_interactions :]:
            messages.append(HumanMessage(content=human))
            messages.append(AIMessage(content=ai))

        messages.append(HumanMessage(content=f"Generate search query for: {question}"))

        resp = self.llm(messages).text
        if resp == "0":
            return Document(content="")

        if resp == "1":
            return Document(content=question)

        return Document(content=resp)


class FullQAPipeline(BaseReasoning):
    """问答管道。处理从问题到答案的整个过程"""

    class Config:
        allow_extra = True

    # 配置参数
    trigger_context: int = 150
    use_rewrite: bool = False

    retrievers: list[BaseComponent]

    evidence_pipeline: PrepareEvidencePipeline = PrepareEvidencePipeline.withx()
    answering_pipeline: AnswerWithContextPipeline = AnswerWithContextPipeline.withx()
    rewrite_pipeline: RewriteQuestionPipeline | None = None
    add_query_context: AddQueryContextPipeline = AddQueryContextPipeline.withx()

    # 添加类变量来跟踪实例
    _instances: ClassVar[set["FullQAPipeline"]] = set()

    def __init__(self, **data):
        super().__init__(**data)
        self.__class__._instances.add(self)

    def update_retrievers(self):
        """更新所有检索器的文档集合"""
        # 检查 retrievers 是否已初始化
        if not hasattr(self, 'retrievers'):
            return

        for idx, retriever in enumerate(self.retrievers):
            if isinstance(retriever, DocumentRetrievalPipeline):
                # 从检索器获取Source模型
                Source = retriever.Source  # 假设DocumentRetrievalPipeline有Source属性

                # 获取最新的文档ID
                with Session(sync_engine) as session:
                    statement = select(Source.id)
                    result = session.execute(statement)
                    doc_ids = list(result.scalars().all())

                # 更新检索器的运行参数
                kwargs = {".doc_ids": doc_ids}
                retriever.set_run(kwargs, temp=False)

                # 更新prepared node
                retriever_node = self._prepare_child(retriever, f"retriever_{idx}")
                self.retrievers[idx] = retriever_node

                logger.info(f"update retriever with doc_ids: {doc_ids}")

    @classmethod
    def get_active_instances(cls) -> list["FullQAPipeline"]:
        """获取所有活动的QA pipeline实例"""
        """获取所有活动且完整初始化的QA pipeline实例"""
        return [
            instance for instance in cls._instances
            if not isinstance(instance.retrievers, unset_)  # 过滤掉未完整初始化的实例
        ]

    def __del__(self):
        """在实例被销毁时从跟踪集合中移除"""
        self.__class__._instances.discard(self)

    def retrieve(
        self, message: str, history: list
    ) -> tuple[list[RetrievedDocument], list[Document]]:
        """根据消息检索文档"""
        # if len(message) < self.trigger_context:
        #     # 对于简短的用户问题，优先添加上下文，避免对长问题添加上下文，因为长问题可能已经包含足够的信息
        #     # 此外，避免原始消息已经太长以至于模型无法处理的情况
        #     query = self.add_query_context(message, history).content
        # else:
        #     query = message
        # logger.info(f"Rewritten query: {query}")
        query = None
        if not query:
            # TODO: 之前返回 [], [] 是因为我们认为这条消息类似于 "Hello", "I need help"...
            query = message

        docs, doc_ids = [], []
        plot_docs = []

        for idx, retriever in enumerate(self.retrievers):
            retriever_node = self._prepare_child(retriever, f"retriever_{idx}")
            retriever_docs = retriever_node(text=query)

            retriever_docs_text = []
            retriever_docs_plot = []

            for doc in retriever_docs:
                if doc.metadata.get("type", "") == "plot":
                    retriever_docs_plot.append(doc)
                else:
                    retriever_docs_text.append(doc)

            for doc in retriever_docs_text:
                if doc.doc_id not in doc_ids:
                    docs.append(doc)
                    doc_ids.append(doc.doc_id)

            plot_docs.extend(retriever_docs_plot)

        info = [
            Document(
                channel="info",
                content=Render.collapsible_with_header(doc, open_collapsible=True),
            )
            for doc in docs
        ] + [
            Document(
                channel="plot",
                content=doc.metadata.get("data", ""),
            )
            for doc in plot_docs
        ]

        return docs, info

    def prepare_citations(self, answer, docs) -> tuple[list[Document], list[Document]]:
        """准备要在UI上显示的引用"""
        with_citation, without_citation = [], []
        spans = defaultdict(list)
        has_llm_score = any("llm_trulens_score" in doc.metadata for doc in docs)

        if answer.metadata["citation"]:
            evidences = answer.metadata["citation"].evidences
            for quote in evidences:
                matched_excerpts = []
                for doc in docs:
                    matches = find_text(quote, doc.text)

                    for start, end in matches:
                        if "|" not in doc.text[start:end]:
                            spans[doc.doc_id].append(
                                {
                                    "start": start,
                                    "end": end,
                                }
                            )
                            matched_excerpts.append(doc.text[start:end])

                # logger.info("Matched citation:", quote, matched_excerpts),

        id2docs = {doc.doc_id: doc for doc in docs}
        not_detected = set(id2docs.keys()) - set(spans.keys())

        # 渲染高亮跨度
        for _id, ss in spans.items():
            if not ss:
                not_detected.add(_id)
                continue
            cur_doc = id2docs[_id]
            highlight_text = ""

            ss = sorted(ss, key=lambda x: x["start"])
            text = cur_doc.text[: ss[0]["start"]]
            for idx, span in enumerate(ss):
                to_highlight = cur_doc.text[span["start"] : span["end"]]
                if len(to_highlight) > len(highlight_text):
                    highlight_text = to_highlight
                text += Render.highlight(to_highlight)
                if idx < len(ss) - 1:
                    text += cur_doc.text[span["end"] : ss[idx + 1]["start"]]
            text += cur_doc.text[ss[-1]["end"] :]
            # 添加到显示列表
            with_citation.append(
                Document(
                    channel="info",
                    content=Render.collapsible_with_header_score(
                        cur_doc,
                        override_text=text,
                        highlight_text=highlight_text,
                        open_collapsible=True,
                    ),
                )
            )

        logger.info("Got {} cited docs".format(len(with_citation)))

        sorted_not_detected_items_with_scores = [
            (id_, id2docs[id_].metadata.get("llm_trulens_score", 0.0))
            for id_ in not_detected
        ]
        sorted_not_detected_items_with_scores.sort(key=lambda x: x[1], reverse=True)

        for id_, _ in sorted_not_detected_items_with_scores:
            doc = id2docs[id_]
            doc_score = doc.metadata.get("llm_trulens_score", 0.0)
            is_open = not has_llm_score or (
                doc_score > CONTEXT_RELEVANT_WARNING_SCORE and len(with_citation) == 0
            )
            without_citation.append(
                Document(
                    channel="info",
                    content=Render.collapsible_with_header_score(
                        doc, open_collapsible=is_open
                    ),
                )
            )
        return with_citation, without_citation

    def show_citations(self, answer, docs):
        # 显示证据
        with_citation, without_citation = self.prepare_citations(answer, docs)
        if not with_citation and not without_citation:
            yield Document(channel="info", content="<h5><b>No evidence found.</b></h5>")
        else:
            # 清除信息面板
            max_llm_rerank_score = max(
                doc.metadata.get("llm_trulens_score", 0.0) for doc in docs
            )
            has_llm_score = any("llm_trulens_score" in doc.metadata for doc in docs)
            # 清除之前的info
            yield Document(channel="info", content=None)

            # 生成警告消息
            if has_llm_score and max_llm_rerank_score < CONTEXT_RELEVANT_WARNING_SCORE:
                yield Document(
                    channel="info",
                    content=(
                        "<h5>WARNING! Context relevance score is low. "
                        "Double check the model answer for correctness.</h5>"
                    ),
                )

            # 显示QA分数
            qa_score = (
                round(answer.metadata["qa_score"], 2)
                if answer.metadata.get("qa_score")
                else None
            )
            if qa_score:
                yield Document(
                    channel="info",
                    content=f"<h5>Answer confidence: {qa_score}</h5>",
                )

            yield from with_citation
            if without_citation:
                yield from without_citation

    async def ainvoke(  # type: ignore
        self, message: str, conv_id: str, history: list, **kwargs  # type: ignore
    ) -> Document:  # type: ignore
        raise NotImplementedError

    def stream(  # type: ignore
        self, message: str, conv_id: str, history: list, **kwargs  # type: ignore
    ) -> Generator[Document, None, Document]:
        if self.use_rewrite and self.rewrite_pipeline:
            logger.info(f"Chosen rewrite pipeline: {self.rewrite_pipeline}")
            message = self.rewrite_pipeline(question=message).text
            logger.info(f"Rewrite result: {message}")

        logger.debug(f"Retrievers stream")  # {self.retrievers}
        # 应该填充上下文
        docs, infos = self.retrieve(message, history)
        logger.info(f"Got {len(docs)} retrieved documents")
        # yield from infos

        def generate_relevant_scores():
            logger.debug(f"generate_relevant_scores under simple.py")
            nonlocal docs
            docs = self.retrievers[0].generate_relevant_scores(message, docs)

        if self.retrievers:
            generate_relevant_scores()

        # # 使用生成相关分数
        # if evidence and self.retrievers:
        #     scoring_thread = threading.Thread(target=generate_relevant_scores)
        #     scoring_thread.start()
        # else:
        #     scoring_thread = None

        evidence_mode, evidence, images = self.evidence_pipeline(docs).content
        logger.info(f"最后的evidence: {evidence}")

        answer = yield from self.answering_pipeline.stream(
            question=message,
            history=history,
            evidence=evidence,
            evidence_mode=evidence_mode,
            images=images,
            conv_id=conv_id,
            **kwargs,
        )

        # 显示证据
        # if scoring_thread:
        #     scoring_thread.join()
        #
        # yield from self.show_citations(answer, docs)

        return answer

    @classmethod
    def get_pipeline(cls, settings, states, retrievers):
        """获取推理管道

        参数:
            settings: 管道的设置
            retrievers: 要使用的检索器
        """
        max_context_length_setting = settings.get("reasoning.max_context_length", 32000)

        pipeline = cls(
            retrievers=retrievers,
            rewrite_pipeline=RewriteQuestionPipeline(),
        )

        prefix = f"reasoning.options.{cls.get_info()['id']}"
        llm_name = settings.get(f"{prefix}.llm", None)
        llm = llms.get(llm_name, llms.get_default())

        # 准备证据管道配置
        evidence_pipeline = pipeline.evidence_pipeline
        evidence_pipeline.max_context_length = max_context_length_setting

        # 回答管道配置
        answer_pipeline = pipeline.answering_pipeline
        answer_pipeline.llm = llm
        answer_pipeline.citation_pipeline.llm = llm
        answer_pipeline.n_last_interactions = settings[f"{prefix}.n_last_interactions"]
        answer_pipeline.enable_citation = settings[f"{prefix}.highlight_citation"]
        answer_pipeline.system_prompt = settings[f"{prefix}.system_prompt"]
        answer_pipeline.qa_template = settings[f"{prefix}.qa_prompt"]
        answer_pipeline.lang = SUPPORTED_LANGUAGE_MAP.get(
            settings["reasoning.lang"], "Chinese"
        )

        pipeline.add_query_context.llm = llm
        pipeline.add_query_context.n_last_interactions = settings[
            f"{prefix}.n_last_interactions"
        ]

        pipeline.trigger_context = settings[f"{prefix}.trigger_context"]
        pipeline.use_rewrite = states.get("app", {}).get("regen", False)
        if pipeline.rewrite_pipeline:
            pipeline.rewrite_pipeline.llm = llm
            pipeline.rewrite_pipeline.lang = SUPPORTED_LANGUAGE_MAP.get(
                settings["reasoning.lang"], "Chinese"
            )
        return pipeline

    @classmethod
    def get_user_settings(cls) -> dict:
        from API.llms.manager import llms

        llm = ""
        choices = [("(default)", "")]
        try:
            choices += [(_, _) for _ in llms.options().keys()]
        except Exception as e:
            logger.exception(f"Failed to get LLM options: {e}")

        return {
            "llm": {
                "name": "语言模型",
                "value": llm,
                "component": "dropdown",
                "choices": choices,
                "special_type": "llm",
                "info": (
                    "用于生成答案的语言模型。如果为None，将使用应用程序默认的语言模型。"
                ),
            },
            "highlight_citation": {
                "name": "高亮引用",
                "value": False,
                "component": "checkbox",
            },
            "system_prompt": {
                "name": "系统提示",
                "value": "这是一个问答系统",
            },
            "qa_prompt": {
                "name": "QA提示（包含 {context}, {question}, {lang}）",
                "value": DEFAULT_QA_TEXT_PROMPT,
            },
            "n_last_interactions": {
                "name": "包含的交互次数",
                "value": 5,
                "component": "number",
                "info": "要包含在LLM中的最大聊天交互次数",
            },
            "trigger_context": {
                "name": "上下文重写的最大消息长度",
                "value": 150,
                "component": "number",
                "info": (
                    "触发上下文添加的最大消息长度。超过此长度，消息将按原样使用。"
                ),
            },
        }

    @classmethod
    def get_info(cls) -> dict:
        return {
            "id": "simple",
            "name": "简单QA",
            "description": (
                "基于RAG的简单问答管道。该管道可以执行关键词搜索和相似性搜索以检索上下文。"
                "之后，它将该上下文包含在内以生成答案。"
            ),
        }


class FullDecomposeQAPipeline(FullQAPipeline):
    def answer_sub_questions(
        self, messages: list, conv_id: str, history: list, **kwargs
    ):
        output_str = ""
        for idx, message in enumerate(messages):
            yield Document(
                channel="chat",
                content=f"<br><b>子问题 {idx + 1}</b>"
                f"<br>{message}<br><b>答案</b><br>",
            )
            # 应该填充上下文
            docs, infos = self.retrieve(message, history)
            logger.info(f"Got {len(docs)} retrieved documents")

            yield from infos

            evidence_mode, evidence, images = self.evidence_pipeline(docs).content
            answer = yield from self.answering_pipeline.stream(
                question=message,
                history=history,
                evidence=evidence,
                evidence_mode=evidence_mode,
                images=images,
                conv_id=conv_id,
                **kwargs,
            )

            output_str += (
                f"子问题 {idx + 1}-th: '{message}'\n答案: '{answer.text}'\n\n"
            )

        return output_str

    def stream(  # type: ignore
        self, message: str, conv_id: str, history: list, **kwargs  # type: ignore
    ) -> Generator[Document, None, Document]:
        sub_question_answer_output = ""
        if self.rewrite_pipeline:
            logger.info(f"Chosen rewrite pipeline {self.rewrite_pipeline}")
            result = self.rewrite_pipeline(question=message)
            logger.info(f"Rewrite result: {result}")
            if isinstance(result, Document):
                message = result.text
            elif (
                isinstance(result, list)
                and len(result) > 0
                and isinstance(result[0], Document)
            ):
                yield Document(
                    channel="chat",
                    content="<h4>子问题及其答案</h4>",
                )
                sub_question_answer_output = yield from self.answer_sub_questions(
                    [r.text for r in result], conv_id, history, **kwargs
                )

        yield Document(
            channel="chat",
            content=f"<h4>主问题</h4>{message}<br><b>答案</b><br>",
        )

        # 应该填充上下文
        docs, infos = self.retrieve(message, history)
        logger.info(f"Got {len(docs)} retrieved documents")
        yield from infos

        evidence_mode, evidence, images = self.evidence_pipeline(docs).content
        answer = yield from self.answering_pipeline.stream(
            question=message,
            history=history,
            evidence=evidence + "\n" + sub_question_answer_output,
            evidence_mode=evidence_mode,
            images=images,
            conv_id=conv_id,
            **kwargs,
        )

        # 显示证据
        with_citation, without_citation = self.prepare_citations(answer, docs)
        if not with_citation and not without_citation:
            yield Document(channel="info", content="<h5><b>No evidence found.</b></h5>")
        else:
            yield Document(channel="info", content=None)
            yield from with_citation
            yield from without_citation

        return answer

    @classmethod
    def get_user_settings(cls) -> dict:
        user_settings = super().get_user_settings()
        user_settings["decompose_prompt"] = {
            "name": "分解提示",
            "value": DecomposeQuestionPipeline.DECOMPOSE_SYSTEM_PROMPT_TEMPLATE,
        }
        return user_settings

    @classmethod
    def get_pipeline(cls, settings, states, retrievers):
        """获取推理管道

        参数:
            settings: 管道的设置
            retrievers: 要使用的检索器
        """
        prefix = f"reasoning.options.{cls.get_info()['id']}"
        pipeline = cls(
            retrievers=retrievers,
            rewrite_pipeline=DecomposeQuestionPipeline(
                prompt_template=settings.get(f"{prefix}.decompose_prompt")
            ),
        )

        llm_name = settings.get(f"{prefix}.llm", None)
        llm = llms.get(llm_name, llms.get_default())

        # 回答管道配置
        answer_pipeline = pipeline.answering_pipeline
        answer_pipeline.llm = llm
        answer_pipeline.citation_pipeline.llm = llm
        answer_pipeline.n_last_interactions = settings[f"{prefix}.n_last_interactions"]
        answer_pipeline.enable_citation = settings[f"{prefix}.highlight_citation"]
        answer_pipeline.system_prompt = settings[f"{prefix}.system_prompt"]
        answer_pipeline.qa_template = settings[f"{prefix}.qa_prompt"]
        answer_pipeline.lang = SUPPORTED_LANGUAGE_MAP.get(
            settings["reasoning.lang"], "Chinese"
        )

        pipeline.add_query_context.llm = llm
        pipeline.add_query_context.n_last_interactions = settings[
            f"{prefix}.n_last_interactions"
        ]

        pipeline.trigger_context = settings[f"{prefix}.trigger_context"]
        pipeline.use_rewrite = states.get("app", {}).get("regen", False)
        if pipeline.rewrite_pipeline:
            pipeline.rewrite_pipeline.llm = llm
        return pipeline

    @classmethod
    def get_info(cls) -> dict:
        return {
            "id": "complex",
            "name": "复杂QA",
            "description": (
                "使用多步推理将复杂问题分解为多个子问题。该管道可以执行关键词搜索和相似性搜索以检索上下文。"
                "之后，它将该上下文包含在内以生成答案。"
            ),
        }