import logging
from sqlmodel import Session, select, or_
from sqlalchemy.ext.asyncio import AsyncSession
from API.db.models import Conversation, User, sync_engine, async_engine
from API.utils.conversation import sync_retrieval_n_message
import flowsettings

logger = logging.getLogger(__name__)


class ConvService:
    def __init__(self, app):
        self._app = app

    async def load_chat_history(self, user_id):
        """重新加载聊天历史"""
        can_see_public: bool = False
        async with AsyncSession(async_engine) as session:
            statement = select(User).where(User.id == user_id)
            # result = await session.exec(statement)
            # user = result.one_or_none()
            result = await session.execute(statement)
            user = result.scalars().one_or_none()

            if user is not None:
                if flowsettings.RAG_USER_CAN_SEE_PUBLIC:
                    can_see_public = (
                            user.username == flowsettings.RAG_USER_CAN_SEE_PUBLIC
                    )
                else:
                    can_see_public = True

        logger.debug(f"用户ID: {user_id}, 可以查看公共对话: {can_see_public}")

        options = []
        async with AsyncSession(async_engine) as session:
            if can_see_public:
                statement = (
                    select(Conversation)
                    .where(
                        or_(
                            Conversation.user == user_id,
                            Conversation.is_public,
                        )
                    )
                    .order_by(
                        Conversation.is_public.desc(), Conversation.date_created.desc()
                    )
                )
            else:
                statement = (
                    select(Conversation)
                    .where(Conversation.user == user_id)
                    .order_by(Conversation.date_created.desc())
                )

            # result = await session.exec(statement)
            # conversations = result.all()
            result = await session.execute(statement)
            conversations = result.scalars().all()
            for conv in conversations:
                options.append((conv.name, conv.id))

        return options

    async def reload_conv(self, user_id):
        conv_list = await self.load_chat_history(user_id)
        if conv_list:
            return conv_list
        else:
            return []

    async def new_conv(self, user_id):
        """创建新聊天"""
        if user_id is None:
            raise ValueError("请先登录")

        async with AsyncSession(async_engine) as session:
            new_conv = Conversation(user=user_id)
            session.add(new_conv)
            await session.commit()
            await session.refresh(new_conv)
            id_ = new_conv.id

        history = await self.load_chat_history(user_id)

        return id_, history

    # async def delete_conv(self, conversation_id, user_id):
    #     """删除选中的对话"""
    #     if not conversation_id:
    #         raise ValueError("未选择对话。")
    #
    #     if user_id is None:
    #         raise ValueError("请先登录")
    #
    #     async with Session(engine) as session:
    #         statement = select(Conversation).where(Conversation.id == conversation_id)
    #         result = await session.exec(statement)
    #         conv = result.one()
    #
    #         await session.delete(conv)
    #         await session.commit()

        history = await self.load_chat_history(user_id)
        if history:
            id_ = history[0][1]
            return id_, history
        else:
            return None, []

    async def select_conv(self, conversation_id, user_id):
        """选择对话"""
        async with AsyncSession(async_engine) as session:
            statement = select(Conversation).where(Conversation.id == conversation_id)
            try:
                result = await session.execute(statement)
                conv = result.scalars().one()
                id_ = conv.id
                name = conv.name
                is_conv_public = conv.is_public

                if user_id == conv.user:
                    selected = conv.data_source.get("selected", {})
                else:
                    selected = {}

                chats = conv.data_source.get("messages", [])
                retrieval_history = conv.data_source.get("retrieval_messages", [])
                plot_history = conv.data_source.get("plot_history", [])

                retrieval_history = sync_retrieval_n_message(chats, retrieval_history)

                info_panel = (
                    retrieval_history[-1]
                    if retrieval_history
                    else "<h5><b>未找到证据。</b></h5>"
                )
                plot_data = plot_history[-1] if plot_history else None
                state = conv.data_source.get("state", {})

            except Exception as e:
                logger.warning(e)
                id_ = ""
                name = ""
                selected = {}
                chats = []
                retrieval_history = []
                plot_history = []
                info_panel = ""
                plot_data = None
                state = {}
                is_conv_public = False

        indices = []
        for index in self._app.index_manager.indices:
            if index.selector is None:
                continue
            if isinstance(index.selector, int):
                indices.append(selected.get(str(index.id), index.default_selector))
            if isinstance(index.selector, tuple):
                indices.extend(selected.get(str(index.id), index.default_selector))

        return (
            id_,
            id_,
            name,
            chats,
            info_panel,
            plot_data,
            retrieval_history,
            plot_history,
            is_conv_public,
            state,
            *indices,
        )

    async def rename_conv(self, conversation_id, new_name, user_id):
        """重命名对话"""
        if user_id is None:
            raise ValueError("请先登录")

        if not conversation_id:
            raise ValueError("未选择对话。")

        if len(new_name) == 0:
            raise ValueError("名称不能为空")
        elif len(new_name) > 40:
            raise ValueError("名称不能超过40个字符")

        async with AsyncSession(async_engine) as session:
            statement = select(Conversation).where(Conversation.id == conversation_id)
            result = await session.execute(statement)
            conv = result.scalars().one()
            conv.name = new_name
            session.add(conv)
            await session.commit()

        history = await self.load_chat_history(user_id)
        return conversation_id, history

