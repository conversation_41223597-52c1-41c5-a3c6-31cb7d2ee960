from pathlib import Path
from datetime import datetime
import csv
from filelock import FileLock
from sqlmodel import Session, select
from copy import deepcopy

from API.pages.chat.conv_service import ConvService
from API.db.models import Conversation, sync_engine
from API.components import reasonings

from RAG.base import Document

DEFAULT_SETTING = "(default)"


class ChatService:
    def __init__(self, app, conv_service: ConvService):
        self._app = app
        self._conv_service = conv_service
        # 初始化其他必要的属性

    def submit_message(self, chat_input, chat_history, user_id, conv_id, conv_name):
        if not chat_input:
            raise ValueError("输入为空")

        if not conv_id:
            # 如果没有会话ID，则创建新的会话
            id_, update = self._create_new_conversation(user_id)
            with Session(sync_engine) as session:
                statement = select(Conversation).where(Conversation.id == id_)
                name = session.exec(statement).one().name
                new_conv_id = id_
                conv_update = update
                new_conv_name = name
        else:
            new_conv_id = conv_id
            conv_update = None  # 这里使用None替代gr.update()
            new_conv_name = conv_name

        return {
            "input": "",  # 清空输入框
            "chat_history": chat_history + [(chat_input, None)],  # 更新聊天记录
            "conv_id": new_conv_id,  # 返回新会话ID
            "conv_update": conv_update,  # 更新会话信息
            "conv_name": new_conv_name,  # 更新会话名称
        }

    async def persist_data_source(
            self,
            convo_id,
            user_id,
            retrieval_msg,
            plot_data,
            retrieval_history,
            plot_history,
            messages,
            state,
            *selecteds
    ):
        if not convo_id:
            return {"error": "未选择会话"}

        try:
            # 将新消息追加到历史中，更新再生状态
            if not state["app"].get("regen", False):
                retrieval_history.append(retrieval_msg)
                plot_history.append(plot_data)
            elif retrieval_history:
                retrieval_history[-1] = retrieval_msg
                plot_history[-1] = plot_data

            # 重置再生状态
            state["app"]["regen"] = False

            # 处理选中的文件信息
            selecteds_ = {}
            for index in self._app.index_manager.indices:
                if index.selector is not None:
                    selecteds_[str(index.id)] = (
                        selecteds[index.selector]
                        if isinstance(index.selector, int)
                        else [selecteds[i] for i in index.selector]
                    )

            # 更新数据库中的会话记录
            async with Session(sync_engine) as session:
                statement = select(Conversation).where(Conversation.id == convo_id)
                result = await session.exec(statement).one()
                is_owner = result.user == user_id
                data_source = result.data_source

                result.data_source = {
                    "selected": selecteds_ if is_owner else data_source.get("selected", {}),
                    "messages": messages,
                    "retrieval_messages": retrieval_history,
                    "plot_history": plot_history,
                    "state": state,
                    "likes": deepcopy(data_source.get("likes", [])),
                }
                session.add(result)
                await session.commit()

            return {"retrieval_history": retrieval_history, "plot_history": plot_history}
        except Exception as e:
            print(f"Error in persist_data_source: {e}")
            return {"error": str(e)}

    def create_pipeline(self, settings, session_reasoning_type, session_llm, state, user_id, *selecteds):
        """创建推理管道，基于应用设置

                参数:
                    settings: 整体设定
                    state: 整体状态
                    selected: 所需文档id的列，如果没有，就是所有文档。

                输出:
                    - 管道对象
                """
        # 针对临时对话页面重写推理模式
        print(f"Session reasoning type: {session_reasoning_type}")
        print(f"Session LLM: {session_llm}")
        reasoning_mode = (
            settings["reasoning.use"]
            if session_reasoning_type in (DEFAULT_SETTING, None)
            else session_reasoning_type
        )
        reasoning_cls = reasonings[reasoning_mode]
        print("Reasoning class", reasoning_cls)
        reasoning_id = reasoning_cls.get_info()["id"]

        settings = deepcopy(settings)
        llm_setting_key = f"reasoning.options.{reasoning_id}.llm"
        if llm_setting_key in settings and session_llm not in (DEFAULT_SETTING, None):
            settings[llm_setting_key] = session_llm
        settings[llm_setting_key] = session_llm

        # 获得检索器
        retrievers = []
        for index in self._app.index_manager.indices:
            index_selected = []
            if isinstance(index.selector, int):
                index_selected = selecteds[index.selector]
            if isinstance(index.selector, tuple):
                for i in index.selector:
                    index_selected.append(selecteds[i])
            iretrievers = index.get_retriever_pipelines(
                settings, user_id, index_selected
            )
            retrievers += iretrievers

        # 准备状态
        reasoning_state = {
            "app": deepcopy(state["app"]),
            "pipeline": deepcopy(state.get(reasoning_id, {})),
        }

        pipeline = reasoning_cls.get_pipeline(settings, reasoning_state, retrievers)

        return pipeline, reasoning_state

    async def chat(self, conversation_id, chat_history, settings, reasoning_type, llm_type, state, user_id, *selecteds):
        chat_input = chat_history[-1][0]
        chat_history = chat_history[:-1]

        pipeline, reasoning_state = self.create_pipeline(
            settings, reasoning_type, llm_type, state, user_id, *selecteds
        )

        text, refs, plot = "", "", None
        msg_placeholder = "思考中..."  # 使用配置或默认值

        async for response in pipeline.stream(chat_input, conversation_id, chat_history):
            if isinstance(response, Document) and response.channel:
                if response.channel == "chat":
                    text += response.content or ""
                elif response.channel == "info":
                    refs += response.content or ""
                elif response.channel == "plot":
                    plot = response.content

                state[pipeline.get_info()["id"]] = reasoning_state["pipeline"]
                yield {
                    "chat_history": chat_history + [(chat_input, text or msg_placeholder)],
                    "refs": refs,
                    "plot": plot,
                    "state": state,
                }

        empty_msg = "抱歉，我不确定。"  # 使用配置或默认值
        yield {
            "chat_history": chat_history + [(chat_input, text or empty_msg)],
            "refs": refs,
            "plot": plot,
            "state": state,
        }

    async def save_log(
            self,
            conversation_id,
            chat_history,
            settings,
            info_panel,
            original_chat_history,
            original_settings,
            original_info_panel,
            log_dir):
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
        log_file = log_path / f"{datetime.now():%Y%m%d_%H}_log.csv"

        async with FileLock(str(log_file) + ".lock"):
            with open(log_file, "a", newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                if not log_file.exists():
                    writer.writerow([
                        "会话ID", "消息ID", "问题", "回答", "聊天记录", "设置信息", "证据", "反馈",
                        "是否原始", "原始回答", "原始聊天记录", "原始设置信息", "原始证据",
                    ])

                async with Session(sync_engine) as session:
                    convo = await session.get(Conversation, conversation_id)
                    if not convo or not convo.data_source.get("likes"):
                        return

                    feedback = convo.data_source["likes"][-1][-1]
                    message_index = convo.data_source["likes"][-1][0]
                    current_message = chat_history[message_index[0]]
                    original_message = original_chat_history[message_index[0]]
                    is_original = current_message == original_message

                    writer.writerow([
                        conversation_id, message_index, current_message[0], current_message[1],
                        chat_history, settings, info_panel, feedback,
                        is_original, original_message[1], original_chat_history,
                        original_settings, original_info_panel
                    ])

    async def _create_new_conversation(self, user_id):
        return await self._conv_service.new_conv(user_id)
