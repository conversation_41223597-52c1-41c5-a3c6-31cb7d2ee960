import uuid
import logging
from typing import Any, Optional, Type

from API.components import filestorage_path, get_docstore, get_vectorstore
from API.db.engine import sync_engine, async_engine
from API.index.base import BaseIndex
from API.index.file.components import FileIndexHandler
from sqlalchemy import JSON, Column, DateTime, Integer, String, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import Session
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.future import select
from sqlalchemy.sql import func
from theflow.settings import settings as flowsettings
from theflow.utils.modules import import_dotted_string

from RAG.storages import BaseDocumentStore, BaseVectorStore

from .base import BaseFileIndexIndexing, BaseFileIndexRetriever

logger = logging.getLogger(__name__)


class FileIndex(BaseIndex):
    """
    文件索引，用于存储和检索文件

    文件索引将文件存储在本地文件夹中，并对其进行索引以供检索。
    该文件索引提供了以下基础设施来支持索引：
        - SQL表 Source：存储系统索引的文件列表
        - 向量存储：包含文件片段的嵌入
        - 文档存储：包含文件片段的文本。存储在文档存储中的每个文本都与向量存储中的向量相关联。
        - SQL表 Index：存储源和文档存储、源和向量存储之间的关系。
    """

    def __init__(self, app, id: int, name: str, config: dict):
        logger.debug(f"Initializing FileIndex with id: {id}")
        super().__init__(app, id, name, config)

        self._indexing_pipeline_cls: Type[BaseFileIndexIndexing]
        self._retriever_pipeline_cls: list[Type[BaseFileIndexRetriever]]

        self._default_settings: dict[str, dict] = {}
        self._setting_mappings: dict[str, dict] = {}
        self._file_handler = FileIndexHandler(app, self)

    def _setup_resources(self):
        """为文件索引设置资源

        资源包括：
            - 数据库表
            - 向量存储
            - 文档存储
            - 文件存储路径
        """
        Base = declarative_base()

        if self.config.get("private", False):
            Source = type(
                "Source",
                (Base,),
                {
                    "__tablename__": f"index__{self.id}__source",
                    "__table_args__": (
                        UniqueConstraint("name", "user", name="_name_user_uc"),
                    ),
                    "id": Column(
                        String,
                        primary_key=True,
                        default=lambda: str(uuid.uuid4()),
                        unique=True,
                    ),
                    "name": Column(String),
                    "path": Column(String),
                    "size": Column(Integer, default=0),
                    "date_created": Column(
                        DateTime(timezone=True), server_default=func.now()
                    ),
                    "user": Column(Integer, default=1),
                    "note": Column(
                        MutableDict.as_mutable(JSON),  # type: ignore
                        default={},
                    ),
                },
            )
        else:
            Source = type(
                "Source",
                (Base,),
                {
                    "__tablename__": f"index__{self.id}__source",
                    "id": Column(
                        String,
                        primary_key=True,
                        default=lambda: str(uuid.uuid4()),
                        unique=True,
                    ),
                    "name": Column(String, unique=True),
                    "path": Column(String),
                    "size": Column(Integer, default=0),
                    "date_created": Column(
                        DateTime(timezone=True), server_default=func.now()
                    ),
                    "user": Column(Integer, default=1),
                    "note": Column(
                        MutableDict.as_mutable(JSON),  # type: ignore
                        default={},
                    ),
                },
            )
        Index = type(
            "IndexTable",
            (Base,),
            {
                "__tablename__": f"index__{self.id}__index",
                "id": Column(Integer, primary_key=True, autoincrement=True),
                "source_id": Column(String),
                "target_id": Column(String),
                "relation_type": Column(String),
                "user": Column(Integer, default=1),
            },
        )

        self._vs: BaseVectorStore = get_vectorstore(f"index_{self.id}")
        self._docstore: BaseDocumentStore = get_docstore(f"index_{self.id}")
        self._fs_path = filestorage_path / f"index_{self.id}"
        self._resources = {
            "Source": Source,
            "Index": Index,
            "VectorStore": self._vs,
            "DocStore": self._docstore,
            "FileStoragePath": self._fs_path,
        }

        # # 使用 Pydantic 验证数据库连接和文件存储路径配置
        # from API.settings import SettingIndexGroup
        #
        # # 验证向量存储和文档存储配置
        # config_settings = SettingIndexGroup()
        # if not config_settings.validate_vectorstore(self._vs):
        #     raise ValueError("VectorStore configuration is invalid")
        # if not config_settings.validate_docstore(self._docstore):
        #     raise ValueError("DocStore configuration is invalid")

    def _setup_indexing_cls(self):
        """获取文件索引的索引类

        只有一个索引类。

        索引类将按以下顺序检索。找到第一个顺序后停止：
            - self.config 中的 `FILE_INDEX_PIPELINE`
            - flowsettings 中的 `FILE_INDEX_{id}_PIPELINE`
            - flowsettings 中的 `FILE_INDEX_PIPELINE`
            - 默认的 .pipelines.IndexDocumentPipeline
        """
        if "FILE_INDEX_PIPELINE" in self.config:
            self._indexing_pipeline_cls = import_dotted_string(
                self.config["FILE_INDEX_PIPELINE"], safe=False
            )
            return

        if hasattr(flowsettings, f"FILE_INDEX_{self.id}_PIPELINE"):
            self._indexing_pipeline_cls = import_dotted_string(
                getattr(flowsettings, f"FILE_INDEX_{self.id}_PIPELINE"), safe=False
            )
            return

        if hasattr(flowsettings, "FILE_INDEX_PIPELINE"):
            self._indexing_pipeline_cls = import_dotted_string(
                getattr(flowsettings, "FILE_INDEX_PIPELINE"), safe=False
            )
            return

        from .pipelines import IndexDocumentPipeline

        self._indexing_pipeline_cls = IndexDocumentPipeline

        # # 通过 Pydantic 验证配置的有效性
        # from API.settings import SettingIndexOption
        #
        # config_option = SettingIndexOption()
        # config_option.validate_pipeline(self._indexing_pipeline_cls)

    def _setup_retriever_cls(self):
        """获取文件索引的检索类

        可以有多个检索类。

        检索类将按以下顺序检索。找到第一个顺序后停止：
            - self.config 中的 `FILE_INDEX_RETRIEVER_PIPELINES`
            - flowsettings 中的 `FILE_INDEX_{id}_RETRIEVER_PIPELINES`
            - flowsettings 中的 `FILE_INDEX_RETRIEVER_PIPELINES`
            - 默认的 .pipelines.DocumentRetrievalPipeline
        """
        if "FILE_INDEX_RETRIEVER_PIPELINES" in self.config:
            self._retriever_pipeline_cls = [
                import_dotted_string(each, safe=False)
                for each in self.config["FILE_INDEX_RETRIEVER_PIPELINES"]
            ]
            return

        if hasattr(flowsettings, f"FILE_INDEX_{self.id}_RETRIEVER_PIPELINES"):
            self._retriever_pipeline_cls = [
                import_dotted_string(each, safe=False)
                for each in getattr(
                    flowsettings, f"FILE_INDEX_{self.id}_RETRIEVER_PIPELINES"
                )
            ]
            return

        if hasattr(flowsettings, "FILE_INDEX_RETRIEVER_PIPELINES"):
            self._retriever_pipeline_cls = [
                import_dotted_string(each, safe=False)
                for each in getattr(flowsettings, "FILE_INDEX_RETRIEVER_PIPELINES")
            ]
            return

        from .pipelines import DocumentRetrievalPipeline

        self._retriever_pipeline_cls = [DocumentRetrievalPipeline]

        # # 验证检索类配置是否符合要求
        # from API.settings import SettingIndexOption
        #
        # config_option = SettingIndexOption()
        # config_option.validate_pipeline_list(self._retriever_pipeline_cls)

    def on_create(self):
        """首次创建索引

        对于文件索引，这将：
            1. 后处理配置
            2. 如果索引和源表不存在，则创建它们
            3. 创建向量存储
            4. 创建文档存储
        """
        # 默认用户的值
        config = {}
        admin_settings = self.get_admin_settings()  # 使用 await 调用
        for key, value in admin_settings.items():
            logger.debug(f"under index.py get_admin_settings, key: {key}, value: {value}")
            config[key] = value["value"]

        # 用户的修改
        config.update(self.config)

        self.config = config

        # 创建资源
        self._setup_resources()
        self._resources["Source"].metadata.create_all(sync_engine)  # type: ignore
        self._resources["Index"].metadata.create_all(sync_engine)  # type: ignore
        self._fs_path.mkdir(parents=True, exist_ok=True)

    def on_delete(self):
        """当用户删除索引时清理索引"""
        import shutil

        self._setup_resources()
        self._resources["Source"].__table__.drop(sync_engine)  # type: ignore
        self._resources["Index"].__table__.drop(sync_engine)  # type: ignore
        self._vs.drop()
        self._docstore.drop()
        shutil.rmtree(self._fs_path)

    def on_start(self):
        """设置类和钩子"""
        self._setup_resources()
        self._setup_indexing_cls()
        self._setup_retriever_cls()

    async def get_user_settings(self) -> dict:
        if self._default_settings:
            return self._default_settings

        settings = {}
        settings.update(await self._indexing_pipeline_cls.get_user_settings())
        for cls in self._retriever_pipeline_cls:
            settings.update(await cls.get_user_settings())

        self._default_settings = settings
        return settings

    @classmethod
    def get_admin_settings(cls):
        logger.debug(f"get_admin_settings under index/file/index.py")
        from API.embeddings.manager import embedding_models_manager
        from API.reranking.manager import reranking_models_manager

        embedding_default = "default"
        embedding_choices = list(embedding_models_manager.options().keys())
        reranking_default = "default"
        reranking_choices = list(reranking_models_manager.options().keys())

        return {
            "embedding": {
                "name": "嵌入模型",
                "value": embedding_default,
                "component": "dropdown",
                "choices": embedding_choices,
                "info": "要使用的嵌入模型的名称。",
            },
            "reranking": {
                "name": "重排模型",
                "value": reranking_default,
                "component": "dropdown",
                "choices": reranking_choices,
                "info": "要使用的重排模型的名称。"
            },
            "supported_file_types": {
                "name": "支持的文件类型",
                "value": ".pdf, .txt",
                "component": "text",
                "info": "可以索引的文件类型，用逗号分隔。",
            },
            "max_file_size": {
                "name": "最大文件大小 (MB)",
                "value": 1000,
                "component": "number",
                "info": "文件的最大大小。设置为0以禁用。",
            },
            "max_number_of_files": {
                "name": "可以索引的最大文件数量",
                "value": 0,
                "component": "number",
                "info": (
                    "系统上可以索引的文件总数。设置为0以禁用。"
                ),
            },
            "private": {
                "name": "设为私有",
                "value": False,
                "component": "radio",
                "choices": [("是", True), ("否", False)],
                "info": "如果为私有，文件将不会跨用户访问。",
            },
        }

    def get_indexing_pipeline(self, settings: dict, user_id: int) -> BaseFileIndexIndexing:
        """定义索引管道的接口"""

        prefix = f"index.options.{self.id}."
        stripped_settings = {k[len(prefix):]: v for k, v in settings.items() if k.startswith(prefix)}

        obj = self._indexing_pipeline_cls.get_pipeline(
            stripped_settings, 
            self.config)
        obj.Source = self._resources["Source"]
        obj.Index = self._resources["Index"]
        obj.VS = self._vs
        obj.DS = self._docstore
        obj.FSPath = self._fs_path
        obj.user_id = user_id
        obj.private = self.config.get("private", False)

        return obj

    # async def get_retriever_pipelines(
    #         self, settings: dict, user_id: int, selected: Any = None
    # ) -> list["BaseFileIndexRetriever"]:
    def get_retriever_pipelines(
            self, settings: dict, user_id: int
    ) -> list["BaseFileIndexRetriever"]:
        # 检索设置
        prefix = f"index.options.{self.id}."
        stripped_settings = {k[len(prefix):]: v for k, v in settings.items() if k.startswith(prefix)}

        # 转换选中的ID
        # selected_ids: Optional[list[str]] = await self._selector_ui.get_selected_ids(selected)
        # 默认是所有的ID
        selected_ids = []
        with Session(sync_engine) as session:
            statement = select(self._resources["Source"].id)
            result = session.exec(statement)

            # 使用结果来遍历并添加到 selected_ids 列表
            # for (id,) in result.scalars().all():
            for id in result.scalars().all():
                selected_ids.append(id)

        retrievers = []
        for cls in self._retriever_pipeline_cls:
            obj = cls.get_pipeline(stripped_settings, self.config, selected_ids)
            if obj is None:
                continue
            obj.Source = self._resources["Source"]
            obj.Index = self._resources["Index"]
            obj.VS = self._vs
            obj.DS = self._docstore
            obj.FSPath = self._fs_path
            obj.user_id = user_id
            retrievers.append(obj)

        return retrievers
