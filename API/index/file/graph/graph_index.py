from typing import Any

from API.index.file import FileIndex

from ..base import BaseFileIndexIndexing, BaseFileIndexRetriever
from .pipelines import GraphRAGIndexingPipeline, GraphRAGRetrieverPipeline


class GraphRAGIndex(FileIndex):
    def _setup_indexing_cls(self):
        # 设置索引管道类
        self._indexing_pipeline_cls = GraphRAGIndexingPipeline

    def _setup_retriever_cls(self):
        # 设置检索器管道类
        self._retriever_pipeline_cls = [GraphRAGRetrieverPipeline]

    def get_indexing_pipeline(self, settings, user_id) -> BaseFileIndexIndexing:
        """定义索引管道的接口"""

        obj = super().get_indexing_pipeline(settings, user_id)
        # 禁用此类型索引的向量存储
        obj.VS = None

        return obj

    def get_retriever_pipelines(
        self, settings: dict, user_id: int
    ) -> list["BaseFileIndexRetriever"]:
        _, file_ids, _ = ['all', [], 1]  # 这里暂时写死，以后有了再改
        retrievers = [
            GraphRAGRetrieverPipeline(
                file_ids=file_ids,
                Index=self._resources["Index"],
            )
        ]

        return retrievers
