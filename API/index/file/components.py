from fastapi import FastAP<PERSON>
from typing import List
import os
import threading
import asyncio
import html
import shutil
import zipfile
import logging
import pandas as pd
from pathlib import Path
from copy import deepcopy
from sqlalchemy import select
from sqlmodel import Session
from sqlalchemy.ext.asyncio import AsyncSession
from API.db.engine import sync_engine, async_engine
from API.utils.render import Render
from API.app import BasePage
from API.reasoning.simple import FullQAPipeline
from theflow.settings import settings as flowsettings

app = FastAPI()
logger = logging.getLogger(__name__)


class DirectoryUpload:
    def __init__(self, index):
        self._index = index
        self._supported_file_types_str = self._index.config.get(
            "supported_file_types", ""
        )
        self._supported_file_types = [
            each.strip() for each in self._supported_file_types_str.split(",")
        ]

    async def upload_directory(self, path: str, reindex: bool = False):
        files = [str(p) for p in Path(path).glob("**/*.*") if p.suffix in self._supported_file_types]
        return await self.index_files(files, reindex)

    async def index_files(self, files: List[str], reindex: bool):
        # 实现文件索引逻辑
        # 这里需要异步实现之前的index_fn逻辑
        pass


class File:
    # 简化的File类，保留核心属性
    def __init__(self, id, name, path, size, type, last_modified):
        self.id = id
        self.name = name
        self.path = path
        self.size = size
        self.type = type
        self.last_modified = last_modified


class FileIndexHandler(BasePage):

    def __init__(self, app, index):
        try:
            # print(f"Initializing FileIndexHandler for index {index.id}")
            super().__init__(app)
            self._index = index
            self._supported_file_types_str = self._index.config.get(
                "supported_file_types", ""
            )
            self._supported_file_types = [
                each.strip() for each in self._supported_file_types_str.split(",")
            ]
            self._vectordb_completion = None  # 添加向量数据库完成事件
            logger.info(f"FileIndexHandler initialized for index {self._index.id}")
        except Exception as e:
            print(f"Error initializing FileIndexHandler: {e}")
            import traceback
            print(traceback.format_exc())

    def upload_instruction(self) -> str:
        """
        返回文件上传约束信息，包括支持的文件类型和大小限制等。

        返回：
            str: 上传约束信息。
        """
        msgs = []
        if self._supported_file_types:
            msgs.append(f"- 支持的文件类型：{self._supported_file_types_str}")

        if max_file_size := self._index.config.get("max_file_size", 0):
            msgs.append(f"- 文件大小上限：{max_file_size} MB")

        if max_number_of_files := self._index.config.get("max_number_of_files", 0):
            msgs.append(f"- 索引最多可包含 {max_number_of_files} 个文件")

        return "\n".join(msgs) if msgs else ""

    def file_selected(self, file_id):
        """处理文件选择逻辑并展示文件内容（如果适用）"""
        chunks = []
        if file_id is not None:
            # 得到切片

            Index = self._index._resources["Index"]
            with Session(sync_engine) as session:
                result = session.execute(
                    select(Index).where(
                        Index.source_id == file_id,
                        Index.relation_type == "document",
                    )
                )
                matches = result.fetchall()
                doc_ids = [doc.target_id for (doc,) in matches]
                docs = self._index._docstore.get(doc_ids)
                docs = sorted(
                    docs, key=lambda x: x.metadata.get("page_label", float("inf"))
                )

                for idx, doc in enumerate(docs):
                    title = html.escape(
                        f"{doc.text[:50]}..." if len(doc.text) > 50 else doc.text
                    )
                    doc_type = doc.metadata.get("type", "text")
                    content = ""
                    if doc_type == "text":
                        content = html.escape(doc.text)
                    elif doc_type == "table":
                        content = Render.table(doc.text)
                    elif doc_type == "image":
                        content = Render.image(
                            url=doc.metadata.get("image_origin", ""), text=doc.text
                        )

                    header_prefix = f"[{idx+1}/{len(docs)}]"
                    if doc.metadata.get("page_label"):
                        header_prefix += f" [Page {doc.metadata['page_label']}]"

                    chunks.append(
                        Render.collapsible(
                            header=f"{header_prefix} {title}",
                            content=content,
                        )
                    )
        return chunks if file_id is not None else []

    async def delete_event(self, file_id):
        file_name = ""
        async with AsyncSession(async_engine) as session:
            source_result = await session.execute(
                select(self._index._resources["Source"]).where(
                    self._index._resources["Source"].id == file_id
                )
            )
            source = source_result.first()
            if source:
                file_name = source[0].name
                session.delete(source[0])

            vs_ids, ds_ids = [], []
            index = session.execute(
                select(self._index._resources["Index"]).where(
                    self._index._resources["Index"].source_id == file_id
                )
            ).all()
            for each in index:
                if each[0].relation_type == "vector":
                    vs_ids.append(each[0].target_id)
                elif each[0].relation_type == "document":
                    ds_ids.append(each[0].target_id)
                session.delete(each[0])
            session.commit()

        if vs_ids:
            self._index._vs.delete(vs_ids)
        self._index._docstore.delete(ds_ids)

        # 返回删除结果
        return {"status": "success", "file_name": file_name} if file_name else {"status": "not_found"}

    def _may_extract_zip(self, files, zip_dir: str):
        """处理ZIP文件并解压到指定目录"""
        zip_files = [file for file in files if file.endswith(".zip")]
        remaining_files = [file for file in files if not file.endswith("zip")]

        # 清理<ZIP目录>，确保每次解压时为干净的目录
        shutil.rmtree(zip_dir, ignore_errors=True)

        for zip_file in zip_files:
            # 解压到各自的文件夹
            basename = os.path.splitext(os.path.basename(zip_file))[0]
            zip_out_dir = os.path.join(zip_dir, basename)
            os.makedirs(zip_out_dir, exist_ok=True)
            with zipfile.ZipFile(zip_file, "r") as zip_ref:
                zip_ref.extractall(zip_out_dir)

        n_zip_file = 0
        for root, dirs, files in os.walk(zip_dir):
            for file in files:
                ext = os.path.splitext(file)[1]

                # 仅允许支持的文件类型（不是 zip）
                if ext not in [".zip"] and ext in self._supported_file_types:
                    remaining_files += [os.path.join(root, file)]
                    n_zip_file += 1

        if n_zip_file > 0:
            print(f"Update zip files: {n_zip_file}")

        return remaining_files

    def index_fn(
            self, files: list[str], reindex: bool, settings: dict, _  # 移除 user_id 参数
    ) -> tuple[list[str], list[str]]:
        """上传并索引文件

        Args:
            files (list[str]): 要索引的文件路径列表
            reindex (bool): 是否重新索引
            settings (dict): 索引相关设置

        Returns:
            tuple[list[str], list[str]]: 包含索引成功与失败的信息
        """

        # print(f"index_fn started for index {self._index.id}")
        # print(f"Received files: {files}")
        # print(f"Reindex: {reindex}")
        # print(f"Settings: {settings}")

        if not files:
            return [], []

        files = self._may_extract_zip(files, flowsettings.RAG_ZIP_INPUT_DIR)

        errors = self.validate(files)
        if errors:
            return [], errors

        logger.info(f"开始索引 {len(files)} 个文件...")

        # 获取索引流水线
        indexing_pipeline = self._index.get_indexing_pipeline(settings, None)
        # print(f"Indexing pipeline under FileIndexHandler: {indexing_pipeline}")

        outputs, debugs = [], []

        try:
            output_stream = indexing_pipeline.stream(files, reindex=reindex)
            # print("Processing next item in output stream")
            for response in output_stream:
                if response is None:
                    # print("Received None response, continuing")
                    continue
                if response.channel == "index":
                    if response.content["status"] == "success":
                        outputs.append(f"\u2705 | {response.content['file_path'].name}")
                    elif response.content["status"] == "failed":
                        outputs.append(
                            f"\u274c | {response.content['file_path'].name}: "
                            f"{response.content['message']}"
                        )
                elif response.channel == "debug":
                    debugs.append(response.text)

            # 更新所有活动的QA pipeline实例
            for pipeline in FullQAPipeline.get_active_instances():
                pipeline.update_retrievers()
        except Exception as e:
            debugs.append(f"Error: {e}")

        print(f"index_fn finished for index {self._index.id}")

        return outputs, debugs

    async def index_fn_with_default_loaders(
            self, files, reindex: bool, settings, _  # 移除 user_id 参数
    ) -> list[str]:
        """快速上传并使用默认加载器

        Args:
            files (list[str]): 要处理的文件路径列表
            reindex (bool): 是否重新索引
            settings (dict): 索引相关设置

        Returns:
            list[str]: 已成功处理的文件 ID 列表
        """
        # print("Overriding with default loaders")
        exist_ids = []
        to_process_files = []

        # # 直接使用 asyncio.run() 来调用异步函数
        # indexing_pipeline = asyncio.run(self._index.get_indexing_pipeline(settings, None))

        # 使用 await 获取异步的 indexing pipeline
        indexing_pipeline = self._index.get_indexing_pipeline(settings, None)

        for str_file_path in files:
            file_path = Path(str(str_file_path))
            exist_id = indexing_pipeline.route(file_path).get_id_if_exists(file_path)
            if exist_id:
                exist_ids.append(exist_id)
            else:
                to_process_files.append(str_file_path)

        returned_ids = []
        settings = deepcopy(settings)
        settings[f"index.options.{self._index.id}.reader_mode"] = "default"
        settings[f"index.options.{self._index.id}.quick_index_mode"] = True

        if to_process_files:
            outputs, _ = self.index_fn(to_process_files, reindex, settings, None)
            returned_ids.extend(outputs)

        return exist_ids + returned_ids

    def index_files_from_dir(
            self, folder_path: str, reindex: bool, settings: dict, _  # 移除 user_id 参数
    ) -> tuple[list[str], list[str]]:
        """
        从指定目录中读取文件并对其进行索引。

        参数：
            folder_path (str): 文件夹的路径。
            reindex (bool): 是否强制重新索引。
            settings (dict): 索引相关的设置。

        步骤：
            1. 从目录读取所有文件。
            2. 应用包含和排除模式，筛选支持的文件类型。
            3. 使用 `index_fn` 方法对文件进行索引。

        返回：
            tuple[list[str], list[str]]: 文件索引结果
        """

        if not folder_path:
            return [], []

        import fnmatch
        from pathlib import Path

        # 允许的文件模式 (此处可自定义为所有文件)
        # include_patterns = ["*"]
        # # 排除的文件模式
        # exclude_patterns = ["*.png", "*.gif", "*/.*"]

        # 从文件夹中读取所有文件
        files = [str(p) for p in Path(folder_path).glob("**/*.*")]

        # # 应用包含模式
        # if include_patterns:
        #     for p in include_patterns:
        #         files = fnmatch.filter(files, p)
        #
        # # 应用排除模式
        # if exclude_patterns:
        #     for p in exclude_patterns:
        #         files = [f for f in files if not fnmatch.fnmatch(f, p)]

        # 使用索引函数对筛选的文件进行索引
        return self.index_fn(files, reindex, settings, None)

    async def handle_upload_and_index(self, files, reindex: bool, settings, task_id: str) -> list[str]:
        """
        处理上传的文件并进行索引

        Args:
            files (list[str]): 要处理的文件路径列表
            reindex (bool): 是否重新索引
            settings (dict): 索引相关设置
            task_id (str): 任务唯一标识符

        Returns:
            list[str]: 索引完成后的文件ID列表
        """
        logger.info(f"Task {task_id}: Starting file upload and index process")
        try:
            # 转换所有文件路径为字符串格式
            files = [str(file) for file in files]

            # 处理压缩包的逻辑调整为仅对 .zip 文件进行解压处理
            zip_files = [file for file in files if file.endswith('.zip')]
            if zip_files:
                # 如果存在压缩文件，则对其进行解压
                files = self._may_extract_zip(zip_files, flowsettings.RAG_ZIP_INPUT_DIR) + [
                    file for file in files if not file.endswith('.zip')
                ]

            # 验证文件
            errors = self.validate(files)
            if errors:
                raise ValueError(f"上传的文件验证失败: {', '.join(errors)}")

            logger.info(f"开始索引 {len(files)} 个文件...")

            # 创建向量数据库完成事件
            self._vectordb_completion = threading.Event()

            # 调用索引方法
            indexed_files = await self.index_fn_with_default_loaders(files, reindex, settings, None)

            return indexed_files
        except Exception as e:
            logger.error(f"Error in handle_upload_and_index: {e}")
            raise

    def format_size_human_readable(self, num: float | str, suffix="B"):
        """将文件大小格式化为人类可读格式"""
        try:
            num = float(num)
        except ValueError:
            return num

        for unit in ("", "K", "M", "G", "T", "P", "E", "Z"):
            if abs(num) < 1024.0:
                return f"{num:3.0f}{unit}{suffix}"
            num /= 1024.0
        return f"{num:.0f}Yi{suffix}"

    async def list_file(self, _, name_pattern=""):
        """
        列出数据库中的文件信息并返回DataFrame格式的文件列表。

        参数：
            name_pattern (str): 按名称模式过滤文件。默认为空，表示显示所有文件。

        返回：
            tuple: 文件信息字典列表 和 Pandas DataFrame 格式的文件列表。
        """

        Source = self._index._resources["Source"]
        async with AsyncSession(async_engine) as session:
            statement = select(Source)
            # 按名称模式过滤文件
            if name_pattern:
                statement = statement.where(Source.name.ilike(f"%{name_pattern}%"))
            result = await session.execute(statement)
            rows = result.fetchall()
            results = [
                {
                    "id": each[0].id,
                    "name": each[0].name,
                    "size": self.format_size_human_readable(each[0].size),
                    "tokens": self.format_size_human_readable(
                        each[0].note.get("tokens", "-"), suffix=""
                    ),
                    "loader": each[0].note.get("loader", "-"),
                    "date_created": each[0].date_created.strftime("%Y-%m-%d %H:%M:%S"),
                }
                for each in rows
            ]

        # 返回文件列表信息
        if results:
            file_list = pd.DataFrame.from_records(results)
        else:
            file_list = pd.DataFrame.from_records(
                [
                    {
                        "id": "-",
                        "name": "-",
                        "size": "-",
                        "tokens": "-",
                        "loader": "-",
                        "date_created": "-",
                    }
                ]
            )

        return results, file_list

    def interact_file_list(self, list_files, selected_index):
        """
        处理文件选择事件以更新文件显示信息。

        参数：
            list_files (DataFrame): 当前显示的文件列表。
            selected_index (int): 选择的文件索引。

        返回：
            tuple: 文件 ID 和格式化后的文件信息。
        """
        if selected_index < 0 or selected_index >= len(list_files):
            # 如果选择的索引不合法
            return None, "已选择文件： (请从上方选择)"

        selected_file = list_files[selected_index]

        if selected_file["id"] == "-":
            return None, "已选择文件： (请从上方选择)"

        return selected_file["id"], f"已选择文件： {selected_file['name']}"

    def validate(self, files: list[str]):
        """
        验证上传文件的合法性。

        参数：
            files (list[str]): 文件路径列表。

        返回：
            list[str]: 错误信息列表。如果验证通过则返回空列表。
        """
        paths = [Path(file) for file in files]
        errors = []

        # 验证文件大小是否超过限制
        if max_file_size := self._index.config.get("max_file_size", 0):
            errors_max_size = []
            for path in paths:
                if path.stat().st_size > max_file_size * 1e6:
                    errors_max_size.append(path.name)
            if errors_max_size:
                str_errors = ", ".join(errors_max_size)
                if len(str_errors) > 60:
                    str_errors = str_errors[:55] + "..."
                errors.append(
                    f"文件大小超出最大限制 ({max_file_size} MB): {str_errors}"
                )

        # 验证文件数量是否超过限制
        if max_number_of_files := self._index.config.get("max_number_of_files", 0):
            with Session(sync_engine) as session:
                result = session.execute(
                    select(self._index._resources["Source"].id)
                )
                current_num_files = len(result.fetchall())
            if len(paths) + current_num_files > max_number_of_files:
                errors.append(
                    f"文件总数超出最大限制 ({max_number_of_files})"
                )

        return errors
