from pathlib import Path
from typing import Generator, Optional

from RAG.base import BaseComponent, Document, Param


class BaseFileIndexRetriever(BaseComponent):

    Source = Param(help="SQLAlchemy 源表")
    Index = Param(help="SQLAlchemy 索引表")
    VS = Param(help="向量存储")
    DS = Param(help="文档存储")
    FSPath = Param(help="文件存储路径")
    user_id = Param(help="用户ID")

    @classmethod
    def get_user_settings(cls) -> dict:
        """获取索引的用户设置

        返回：
            dict: 用户设置，格式为 `API.settings.SettingItem` 的字典
        """
        return {}

    @classmethod
    def get_pipeline(
        cls,
        user_settings: dict,
        index_settings: dict,
        selected: Optional[list] = None,
    ) -> "BaseFileIndexRetriever":
        raise NotImplementedError


class BaseFileIndexIndexing(BaseComponent):
    """将信息索引到数据存储的管道

    你应该定义以下方法：
        - run(self, file_paths): 给定管道运行索引
        - get_pipeline(cls, user_settings, index_settings): 返回完全初始化的管道，准备由 API 使用

    你将可以访问以下资源：
        - self._Source: 源表
        - self._Index: 索引表
        - self._VS: 向量存储
        - self._DS: 文档存储
    """

    Source = Param(help="SQLAlchemy 源表")
    Index = Param(help="SQLAlchemy 索引表")
    VS = Param(help="向量存储")
    DS = Param(help="文档存储")
    FSPath = Param(help="文件存储路径")
    user_id = Param(help="用户ID")
    private = Param(False, help="是否为私有索引")

    def run(
        self, file_paths: str | Path | list[str | Path], *args, **kwargs
    ) -> tuple[list[str | None], list[str | None]]:
        """运行索引管道

        参数：
            file_paths (str | Path | list[str | Path]): 要索引的文件路径

        返回：
            - 索引的文件ID（每个文件ID对应一个输入文件路径，如果索引失败则为None）
            - 错误消息（每个错误消息对应一个输入文件路径，如果索引成功则为None）
        """
        raise NotImplementedError

    def stream(
        self, file_paths: str | Path | list[str | Path], *args, **kwargs
    ) -> Generator[
        Document, None, tuple[list[str | None], list[str | None], list[Document]]
    ]:
        """流式索引管道

        参数：
            file_paths (str | Path | list[str | Path]): 要索引的文件路径

        生成：
            Document: 输出消息到UI，必须有 channel == index 或 debug

        返回：
            - 索引的文件ID（每个文件ID对应一个输入文件路径，如果索引失败则为None）
            - 错误消息（每个错误消息对应一个输入文件路径，如果索引成功则为None）
            - 索引的文档列表
        """
        raise NotImplementedError

    @classmethod
    def get_pipeline(
        cls, user_settings: dict, index_settings: dict
    ) -> "BaseFileIndexIndexing":
        raise NotImplementedError

    @classmethod
    def get_user_settings(cls) -> dict:
        """获取索引的用户设置

        返回：
            dict: 用户设置，格式为 `API.settings.SettingItem` 的字典
        """
        return {}

    def copy_to_filestorage(
        self, file_paths: str | Path | list[str | Path]
    ) -> list[str]:
        """复制到文件存储并返回新路径，相对于文件存储

        参数：
            file_path: 要复制的文件路径

        返回：
            相对于文件存储的新文件路径
        """
        import shutil
        from hashlib import sha256

        if not isinstance(file_paths, list):
            file_paths = [file_paths]

        paths = []
        for file_path in file_paths:
            with open(file_path, "rb") as f:
                paths.append(sha256(f.read()).hexdigest())
            shutil.copy(file_path, self.FSPath / paths[-1])

        return paths

    def get_filestorage_path(self, rel_paths: str | list[str]) -> list[str]:
        """获取相对路径的文件存储路径

        参数：
            rel_paths: 相对于文件存储的路径

        返回：
            文件的绝对文件存储路径
        """
        raise NotImplementedError

    def warning(self, msg):
        """记录警告消息

        参数：
            msg: 要记录的消息
        """
        print(msg)

    def rebuild_index(self):
        """重建索引"""
        raise NotImplementedError
