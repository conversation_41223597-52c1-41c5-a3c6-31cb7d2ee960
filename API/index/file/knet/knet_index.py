from typing import Any

from API.index.file import FileIndex

from ..base import BaseFileIndexIndexing, BaseFileIndexRetriever
from .pipelines import KnetIndexingPipeline, KnetRetrievalPipeline


class KnowledgeNetworkFileIndex(FileIndex):
    @classmethod
    def get_admin_settings(cls):
        admin_settings = super().get_admin_settings()

        # 从管理员设置中移除嵌入，因为我们不需要它
        admin_settings.pop("embedding")
        return admin_settings

    def _setup_indexing_cls(self):
        self._indexing_pipeline_cls = KnetIndexingPipeline

    def _setup_retriever_cls(self):
        self._retriever_pipeline_cls = [KnetRetrievalPipeline]

    def get_indexing_pipeline(self, settings, user_id) -> BaseFileIndexIndexing:
        """定义索引管道的接口"""

        obj = super().get_indexing_pipeline(settings, user_id)
        # 禁用此类索引的向量存储
        # 同时为 API 调用设置集合名称
        obj.VS = None
        obj.collection_name = f"kh_index_{self.id}"

        return obj

    def get_retriever_pipelines(
        self, settings: dict, user_id: int, selected: Any = None
    ) -> list["BaseFileIndexRetriever"]:
        retrievers = super().get_retriever_pipelines(settings, user_id, selected)

        for obj in retrievers:
            # 禁用此类索引的向量存储
            # 同时为 API 调用设置集合名称
            obj.VS = None
            obj.collection_name = f"kh_index_{self.id}"

        return retrievers