import base64
import json
import os
from pathlib import Path
from typing import Optional, Sequence

import requests
import yaml

from RAG.base import RetrievedDocument
from RAG.indices.rankings import BaseReranking, LLMReranking, LLMTrulensScoring

from ..pipelines import BaseFileIndexRetriever, IndexDocumentPipeline, IndexPipeline


class KnetIndexingPipeline(IndexDocumentPipeline):
    """知识网络特定的索引管道"""

    # 外部索引调用的集合名称
    collection_name: str = "default"

    @classmethod
    def get_user_settings(cls):
        return {
            "reader_mode": {
                "name": "索引解析器",
                "value": "knowledge_network",
                "choices": [
                    ("默认 (KN)", "knowledge_network"),
                ],
                "component": "dropdown",
            },
        }

    def route(self, file_path: Path) -> IndexPipeline:
        """简单地禁用此管道的拆分器（分块）"""
        pipeline = super().route(file_path)
        pipeline.splitter = None
        # 将 IndexPipeline 集合名称分配给加载器
        pipeline.collection_name = self.collection_name

        return pipeline


class KnetRetrievalPipeline(BaseFileIndexRetriever):
    DEFAULT_KNET_ENDPOINT: str = "http://127.0.0.1:8081/retrieve"

    collection_name: str = "default"
    rerankers: Sequence[BaseReranking] = [LLMReranking.withx()]

    def encode_image_base64(self, image_path: str | Path) -> bytes | str:
        """将图像转换为 base64 编码"""
        img_base64 = "data:image/png;base64,{}"
        with open(image_path, "rb") as image_file:
            return img_base64.format(
                base64.b64encode(image_file.read()).decode("utf-8")
            )

    def run(
        self,
        text: str,
        doc_ids: Optional[list[str]] = None,
        *args,
        **kwargs,
    ) -> list[RetrievedDocument]:
        """检索与文本相似的文档片段

        Args:
            text: 用于检索相似文档的文本
            doc_ids: 用于约束检索的文档 ID 列表
        """
        print("在 doc_ids 中搜索", doc_ids)
        if not doc_ids:
            return []

        docs: list[RetrievedDocument] = []
        params = {
            "query": text,
            "collection": self.collection_name,
            "meta_filters": {"doc_name": doc_ids},
        }
        params["meta_filters"] = json.dumps(params["meta_filters"])
        response = requests.get(self.DEFAULT_KNET_ENDPOINT, params=params)
        metadata_translation = {
            "TABLE": "table",
            "FIGURE": "image",
        }

        if response.status_code == 200:
            # 从响应内容中加载 YAML 内容
            chunks = yaml.safe_load(response.content)
            for chunk in chunks:
                metadata = chunk["node"]["metadata"]
                metadata["type"] = metadata_translation.get(
                    metadata.pop("content_type", ""), ""
                )
                metadata["file_name"] = metadata.pop("company_name", "")

                # 从返回的路径加载图像
                image_path = metadata.get("image_path", "")
                if image_path and os.path.isfile(image_path):
                    base64_im = self.encode_image_base64(image_path)
                    # 显式设置文档类型
                    metadata["type"] = "image"
                    metadata["image_origin"] = base64_im

                docs.append(
                    RetrievedDocument(text=chunk["node"]["text"], metadata=metadata)
                )
        else:
            raise IOError(f"{response.status_code}: {response.text}")

        for reranker in self.rerankers:
            docs = reranker(documents=docs, query=text)

        return docs

    @classmethod
    def get_user_settings(cls) -> dict:
        from API.llms.manager import llms

        try:
            reranking_llm = llms.get_default_name()
            reranking_llm_choices = list(llms.options().keys())
        except Exception:
            reranking_llm = None
            reranking_llm_choices = []

        return {
            "reranking_llm": {
                "name": "用于评分的 LLM",
                "value": reranking_llm,
                "component": "dropdown",
                "choices": reranking_llm_choices,
                "special_type": "llm",
            },
            "retrieval_mode": {
                "name": "检索模式",
                "value": "hybrid",
                "choices": ["vector", "text", "hybrid"],
                "component": "dropdown",
            },
        }

    @classmethod
    def get_pipeline(cls, user_settings, index_settings, selected):
        """获取与索引关联的检索器对象

        Args:
            settings: 应用的设置
            kwargs: 其他参数
        """
        from API.llms.manager import llms

        retriever = cls(
            rerankers=[LLMTrulensScoring()],
        )

        # 通过 flow 将 doc_ids 输入到 retriever.run() 调用中的 hacky 方式
        kwargs = {".doc_ids": selected}
        retriever.set_run(kwargs, temp=False)

        for reranker in retriever.rerankers:
            if isinstance(reranker, LLMReranking):
                reranker.llm = llms.get(
                    user_settings["reranking_llm"], llms.get_default()
                )

        return retriever