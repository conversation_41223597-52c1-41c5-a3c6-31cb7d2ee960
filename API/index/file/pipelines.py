from __future__ import annotations

import logging
import shutil
import threading
import time
import warnings
import asyncio
from collections import defaultdict
from copy import deepcopy
from functools import lru_cache
from hashlib import sha256
from pathlib import Path
from typing import AsyncGenerator, Generator, Optional, Sequence
from datetime import datetime

import tiktoken
from API.db.models import async_engine, sync_engine
from API.embeddings.manager import embedding_models_manager
from API.reranking.manager import reranking_models_manager
from API.llms.manager import llms
from llama_index.core.readers.base import BaseReader
from llama_index.core.readers.file.base import default_file_metadata_func
from llama_index.core.vector_stores import (
    FilterCondition,
    FilterOperator,
    MetadataFilter,
    MetadataFilters,
)
from llama_index.core.vector_stores.types import VectorStoreQueryMode
from sqlalchemy import delete, select
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from theflow.settings import settings
from theflow.utils.modules import import_dotted_string

from RAG.base import BaseComponent, Document, Node, Param, RetrievedDocument
from RAG.embeddings import BaseEmbeddings
from RAG.indices import VectorIndexing, VectorRetrieval
from RAG.indices.ingests.files import (
    RAG_DEFAULT_FILE_EXTRACTORS,
    unstructured,
)
from RAG.indices.rankings import BaseReranking, LLMReranking
from RAG.indices.splitters import BaseSplitter, TokenSplitter, RecursiveCharacterSplitter
from RAG.reranking import BaseReranking, CohereReranking, LocalBGEReranking
from RAG.utils.check_pdf import is_scanned_pdf
from RAG.loaders import OCRReader, PDFThumbnailReader
from .base import BaseFileIndexIndexing, BaseFileIndexRetriever

logger = logging.getLogger(__name__)


@lru_cache
def dev_settings():
    """从flowsettings.py中检索开发者设置"""
    file_extractors = {}

    if hasattr(settings, "FILE_INDEX_PIPELINE_FILE_EXTRACTORS"):
        file_extractors = {
            key: import_dotted_string(value, safe=False)()
            for key, value in settings.FILE_INDEX_PIPELINE_FILE_EXTRACTORS.items()
        }

    chunk_size = None
    if hasattr(settings, "FILE_INDEX_PIPELINE_SPLITTER_CHUNK_SIZE"):
        chunk_size = settings.FILE_INDEX_PIPELINE_SPLITTER_CHUNK_SIZE

    chunk_overlap = None
    if hasattr(settings, "FILE_INDEX_PIPELINE_SPLITTER_CHUNK_OVERLAP"):
        chunk_overlap = settings.FILE_INDEX_PIPELINE_SPLITTER_CHUNK_OVERLAP

    return file_extractors, chunk_size, chunk_overlap


_default_token_func = tiktoken.encoding_for_model("gpt-3.5-turbo").encode


class DocumentRetrievalPipeline(BaseFileIndexRetriever):
    """检索相关文档

    参数：
        vector_retrieval: 检索管道，根据文本查询返回相关文档
        reranker: 重新排序管道，对检索到的文档进行重新排序和过滤
        get_extra_table: 如果为True，对于每个检索到的文档，管道将查找周围的表格（例如在页面上）
        top_k: 要检索的文档数量
        mmr: 是否使用mmr对文档进行重新排序
    """

    embedding: BaseEmbeddings
    rerankers: Sequence[BaseReranking] = []
    # 使用LLM为UI创建相关分数
    llm_scorer: LLMReranking | None = LLMReranking.withx()
    get_extra_table: bool = False
    mmr: bool = False
    top_k: int = 5
    retrieval_mode: str = "hybrid"

    @Node.auto(depends_on=["embedding", "VS", "DS"])
    def vector_retrieval(self) -> VectorRetrieval:
        return VectorRetrieval(
            embedding=self.embedding,
            vector_store=self.VS,
            doc_store=self.DS,
            retrieval_mode=self.retrieval_mode,  # type: ignore
            rerankers=self.rerankers,
        )

    def run(
        self,
        text: str,
        doc_ids: Optional[list[str]] = None,
        *args,
        **kwargs,
    ) -> list[RetrievedDocument]:
        """检索与文本相似的文档片段

        参数：
            text: 用于检索相似文档的文本
            doc_ids: 约束检索的文档ID列表
        """
        logger.debug(f"在doc_ids中搜索: {doc_ids}")
        if not doc_ids:
            logger.info(f"由于未选择文件，跳过检索: {self}")
            return []

        retrieval_kwargs: dict = {}
        with Session(sync_engine) as session:
            stmt = select(self.Index).where(
                self.Index.relation_type == "document",
                self.Index.source_id.in_(doc_ids),
            )
            results = session.execute(stmt)
            chunk_ids = [r[0].target_id for r in results.all()]

        # 进行第一轮top_k扩展
        retrieval_kwargs["do_extend"] = True
        retrieval_kwargs["scope"] = chunk_ids
        retrieval_kwargs["filters"] = MetadataFilters(
            filters=[
                MetadataFilter(
                    key="file_id",
                    value=doc_ids,
                    operator=FilterOperator.IN,
                )
            ],
            condition=FilterCondition.OR,
        )

        if self.mmr:
            # TODO: 再次检查llama-index MMR是否正确工作
            retrieval_kwargs["mode"] = VectorStoreQueryMode.MMR
            retrieval_kwargs["mmr_threshold"] = 0.5

        # 重新排序
        s_time = time.time()
        logger.debug(f"retrieval_kwargs: {retrieval_kwargs.keys()}")
        docs = self.vector_retrieval(text=text, top_k=self.top_k, **retrieval_kwargs)
        logger.info(f"检索步骤耗时: {time.time() - s_time}")

        if not self.get_extra_table:
            return docs

        # 检索与表格相关的额外节点
        table_pages = defaultdict(list)
        retrieved_id = set([doc.doc_id for doc in docs])
        for doc in docs:
            if "page_label" not in doc.metadata:
                continue
            if "file_name" not in doc.metadata:
                warnings.warn(
                    "file_name不在metadata中，而page_label在metadata中: "
                    f"{doc.metadata}"
                )
            table_pages[doc.metadata["file_name"]].append(doc.metadata["page_label"])

        queries: list[dict] = [
            {"$and": [{"file_name": {"$eq": fn}}, {"page_label": {"$in": pls}}]}
            for fn, pls in table_pages.items()
        ]
        if queries:
            try:
                extra_docs = self.vector_retrieval(
                    text="",
                    top_k=50,
                    where=queries[0] if len(queries) == 1 else {"$or": queries},
                )
                for doc in extra_docs:
                    if doc.doc_id not in retrieved_id:
                        docs.append(doc)
            except Exception:
                logger.info("检索额外表格时出错")

        return docs

    def generate_relevant_scores(
        self, query: str, documents: list[RetrievedDocument]
    ) -> list[RetrievedDocument]:
        logger.debug(f"generate_relevant_scores under file/pipelines")
        docs = (
            documents
            if not self.llm_scorer
            else self.llm_scorer(documents=documents, query=query)
        )
        return docs

    @classmethod
    def get_user_settings(cls) -> dict:
        logger.debug(f"get_user_settings under file/pipelines")
        from API.llms.manager import llms

        try:
            reranking_llm = llms.get_default_name()
            reranking_llm_choices = list(llms.options().keys())
        except Exception as e:
            logger.error(e)
            reranking_llm = None
            reranking_llm_choices = []

        return {
            "reranking_llm": {
                "name": "用于相关分数的LLM",
                "value": reranking_llm,
                "component": "dropdown",
                "choices": reranking_llm_choices,
                "special_type": "llm",
            },
            "num_retrieval": {
                "name": "要检索的文档块数量",
                "value": 10,
                "component": "number",
            },
            "retrieval_mode": {
                "name": "检索模式",
                "value": "hybrid",
                "choices": ["vector", "text", "hybrid"],
                "component": "dropdown",
            },
            "prioritize_table": {
                "name": "优先表格",
                "value": False,
                "choices": [True, False],
                "component": "checkbox",
            },
            "mmr": {
                "name": "使用MMR",
                "value": False,
                "choices": [True, False],
                "component": "checkbox",
            },
            "use_reranking": {
                "name": "使用重新排序",
                "value": True,
                "choices": [True, False],
                "component": "checkbox",
            },
            "use_llm_reranking": {
                "name": "使用LLM相关分数",
                "value": True,
                "choices": [True, False],
                "component": "checkbox",
            },
        }

    @classmethod
    def get_pipeline(cls, user_settings, index_settings, selected):
        """获取与索引关联的检索器对象

        参数：
            settings: 应用程序的设置
            kwargs: 其他参数
        """
        from flowsettings import DOCUMENT_RETRIEVAL_SETTINGS as settings
        use_llm_reranking = settings.get("use_llm_reranking", False)
        test_reranker = reranking_models_manager["default"]
        logger.debug(f"test_reranker: {test_reranker}")

        retriever = cls(
            get_extra_table=settings["prioritize_table"],
            top_k=settings["num_retrieval"],
            mmr=settings["mmr"],
            embedding=embedding_models_manager[
                index_settings.get(
                    "embedding", embedding_models_manager.get_default_name()
                )
            ],
            retrieval_mode=settings["retrieval_mode"],
            llm_scorer=(LLMReranking() if use_llm_reranking else None),
            rerankers=[reranking_models_manager["default"]]
        )
        if not settings["use_reranking"]:
            retriever.rerankers = []  # type: ignore

        for reranker in retriever.rerankers:
            if isinstance(reranker, LLMReranking):
                reranker.llm = llms.get(
                    settings["reranking_llm"], llms.get_default()
                )

        if retriever.llm_scorer:
            retriever.llm_scorer.llm = llms.get(
                settings["reranking_llm"], llms.get_default()
            )

        kwargs = {".doc_ids": selected}
        retriever.set_run(kwargs, temp=False)
        return retriever


class IndexPipeline(BaseComponent):
    """索引单个文件"""

    loader: BaseReader
    splitter: BaseSplitter | None
    chunk_batch_size: int = 200
    Source = Param(help="SQLAlchemy 源表")
    Index = Param(help="SQLAlchemy 索引表")
    VS = Param(help="向量存储")
    DS = Param(help="文档存储")
    FSPath = Param(help="文件存储路径")
    user_id = Param(help="用户ID")
    collection_name: str = "default"
    private: bool = False
    run_embedding_in_thread: bool = False
    embedding: BaseEmbeddings

    @Node.auto(depends_on=["Source", "Index", "embedding"])
    def vector_indexing(self) -> VectorIndexing:
        return VectorIndexing(
            vector_store=self.VS, doc_store=self.DS, embedding=self.embedding
        )

    def handle_docs(self, docs, file_id, file_name) -> Generator[Document, None, int]:
        # logger.debug("Running handle_docs under IndexPipeline.")
        s_time = time.time()
        text_docs = []
        non_text_docs = []
        thumbnail_docs = []

        for doc in docs:
            doc_type = doc.metadata.get("type", "text")
            if doc_type == "text":
                text_docs.append(doc)
            elif doc_type == "thumbnail":
                thumbnail_docs.append(doc)
            else:
                non_text_docs.append(doc)

        # logger.debug(f"获取到 {len(thumbnail_docs)} 个页面缩略图")
        page_label_to_thumbnail = {
            doc.metadata["page_label"]: doc.doc_id for doc in thumbnail_docs
        }

        if self.splitter:
            all_chunks = self.splitter(text_docs)
        else:
            all_chunks = text_docs

        # 将缩略图的doc_id添加到chunks中
        for chunk in all_chunks:
            page_label = chunk.metadata.get("page_label", None)
            chunk_len = len(chunk.text)
            if page_label and page_label in page_label_to_thumbnail:
                chunk.metadata["thumbnail_doc_id"] = page_label_to_thumbnail[page_label]

        to_index_chunks = all_chunks + non_text_docs + thumbnail_docs
        # logger.debug(f"all_chunks chunk lengths: {[len(chunk.page_content) for chunk in all_chunks]}")
        # logger.debug(f"to_index_chunks chunk lengths: {[len(chunk.page_content) for chunk in to_index_chunks]}")

        # max_content_length = max([len(doc.page_content) for doc in all_chunks])
        # if max_content_length > 1800:
        #     longest_content = max(all_chunks, key=lambda doc: len(doc.page_content))
        #     logger.debug(f"Longest content length: {max_content_length}, Content: {longest_content}")

        # 添加到文档存储
        chunks = []
        n_chunks = 0
        # logger.debug(f"Embedding vector under IndexPipeline for {file_name}, chunk {n_chunks}")
        chunk_size = self.chunk_batch_size * 4
        for start_idx in range(0, len(to_index_chunks), chunk_size):
            chunks = to_index_chunks[start_idx: start_idx + chunk_size]
            self.handle_chunks_docstore(chunks, file_id)
            n_chunks += len(chunks)
            yield Document(
                f" => [{file_name}] 处理了 {n_chunks} 个chunks",
                channel="debug",
            )

        # 创建一个Event来追踪向量数据库写入的完成
        self._vectordb_completion = threading.Event()

        def insert_chunks_to_vectorstore():
            try:
                logger.debug("insert_chunks_to_vectorstore")
                chunks = []
                n_chunks = 0
                chunk_size = self.chunk_batch_size
                for start_idx in range(0, len(to_index_chunks), chunk_size):
                    chunks = to_index_chunks[start_idx: start_idx + chunk_size]
                    self.handle_chunks_vectorstore(chunks, file_id)
                    n_chunks += len(chunks)
                    if self.VS:
                        yield Document(
                            f" => [{file_name}] 为 {n_chunks} 个chunks创建了嵌入",
                            channel="debug",
                        )
            finally:
                # 无论成功失败都标记完成
                self._vectordb_completion.set()

        # 如果指定，在线程中运行向量索引
        if self.run_embedding_in_thread:
            logger.debug("在线程中运行嵌入")
            threading.Thread(
                target=lambda: list(insert_chunks_to_vectorstore())
            ).start()
        else:
            yield from insert_chunks_to_vectorstore()
            self._vectordb_completion.set()

        # 等待向量数据库写入完成
        self._vectordb_completion.wait()

        logger.info(f"索引步骤耗时: {time.time() - s_time}")
        return n_chunks

    def handle_chunks_docstore(self, chunks, file_id):
        """处理chunks"""
        # 运行嵌入，添加到向量存储和文档存储
        self.vector_indexing.add_to_docstore(chunks)

        # 记录在索引中
        with Session(sync_engine) as session:
            nodes = []
            for chunk in chunks:
                nodes.append(
                    self.Index(
                        source_id=file_id,
                        target_id=chunk.doc_id,
                        relation_type="document",
                    )
                )
            session.add_all(nodes)
            session.commit()

    def handle_chunks_vectorstore(self, chunks, file_id):
        """处理chunks"""
        # 运行嵌入，添加到向量存储和文档存储
        # logger.debug(f"handle_chunks_vectorstore")
        # logger.debug(f"chunks chunk lengths: {[len(chunk.page_content) for chunk in chunks]}")
        self.vector_indexing.add_to_vectorstore(chunks)
        self.vector_indexing.write_chunk_to_file(chunks)

        if self.VS:
            # 记录在索引中
            with Session(sync_engine) as session:
                nodes = []
                for chunk in chunks:
                    nodes.append(
                        self.Index(
                            source_id=file_id,
                            target_id=chunk.doc_id,
                            relation_type="vector",
                        )
                    )
                session.add_all(nodes)
                session.commit()

    def get_id_if_exists(self, file_path: Path) -> Optional[str]:
        """检查文件是否已索引

        参数：
            file_path: 文件路径

        返回：
            如果文件已索引，返回文件ID，否则返回None
        """
        if self.private:
            cond: tuple = (
                self.Source.name == file_path.name,
                self.Source.user == self.user_id,
            )
        else:
            cond = (self.Source.name == file_path.name,)

        with Session(sync_engine) as session:
            stmt = select(self.Source).where(*cond)
            item = session.execute(stmt).first()
            if item:
                return item[0].id

        return None

    def update_selected_ids(self, selected_ids: list[str]):
        """更新检索器的selected_ids"""
        for retriever in self.retrievers:
            retriever.selected_ids = selected_ids  # 直接更新检索器的selected_ids
        logger.debug(f"Updated selected_ids: {selected_ids}")

    def store_file(self, file_path: Path) -> str:
        """将文件存储到数据库和存储中，返回文件ID

        参数：
            file_path: 文件路径

        返回：
            文件ID
        """
        with file_path.open("rb") as fi:
            file_hash = sha256(fi.read()).hexdigest()

        shutil.copy(file_path, self.FSPath / file_hash)
        source = self.Source(
            name=file_path.name,
            path=file_hash,
            size=file_path.stat().st_size,
            user=self.user_id,  # type: ignore
        )
        with Session(sync_engine) as session:
            session.add(source)
            session.commit()
            session.refresh(source)
            file_id = source.id

        return file_id

    def finish(self, file_id: str, file_path: Path) -> str:
        """完成索引"""
        with Session(sync_engine) as session:
            stmt = select(self.Source).where(self.Source.id == file_id)
            result = session.execute(stmt).first()
            if not result:
                return file_id

            item = result[0]

            # 填充token数量
            doc_ids_stmt = select(self.Index.target_id).where(
                self.Index.source_id == file_id,
                self.Index.relation_type == "document",
            )
            doc_ids = [_[0] for _ in session.execute(doc_ids_stmt)]
            token_func = self.get_token_func()
            if doc_ids and token_func:
                docs = self.DS.get(doc_ids)
                item.note["tokens"] = sum([len(token_func(doc.text)) for doc in docs])

            # 填充note
            item.note["loader"] = self.get_from_path("loader").__class__.__name__

            session.add(item)
            session.commit()

        return file_id

    def get_token_func(self):
        """获取用于计算token数量的token函数"""
        return _default_token_func

    def delete_file(self, file_id: str):
        """从数据库中删除文件，包括其在文档存储和向量存储中的chunks

        参数：
            file_id: 文件ID
        """
        with Session(sync_engine) as session:
            session.execute(delete(self.Source).where(self.Source.id == file_id))
            vs_ids, ds_ids = [], []
            index = session.execute(
                select(self.Index).where(self.Index.source_id == file_id)
            ).all()
            for each in index:
                if each[0].relation_type == "vector":
                    vs_ids.append(each[0].target_id)
                elif each[0].relation_type == "document":
                    ds_ids.append(each[0].target_id)
                session.delete(each[0])
            session.commit()

        if vs_ids and self.VS:
            self.VS.delete(vs_ids)
        if ds_ids:
            self.DS.delete(ds_ids)

    def run(
        self, file_path: str | Path, reindex: bool, **kwargs
    ) -> tuple[str, list[Document]]:
        raise NotImplementedError

    def stream(
        self, file_path: str | Path, reindex: bool, **kwargs
    ) -> Generator[Document, None, tuple[str, list[Document]]]:
        # 检查重复
        # logger.debug(f"Indexing file under IndexPipeline: {file_path}")
        file_path = Path(file_path).resolve()
        file_id = self.get_id_if_exists(file_path)
        if file_id is not None:
            if not reindex:
                raise ValueError(
                    f"文件 {file_path.name} 已索引。请使用 reindex=True 强制重新索引。"
                )
            else:
                # 移除现有记录
                yield Document(f" => 移除旧的 {file_path.name}", channel="debug")
                self.delete_file(file_id)
                file_id = self.store_file(file_path)
        else:
            # 添加记录到数据库
            file_id = self.store_file(file_path)

        # 提取文件
        extra_info = default_file_metadata_func(str(file_path))
        extra_info["file_id"] = file_id
        extra_info["collection_name"] = self.collection_name

        yield Document(f" => 将 {file_path.name} 转换为文本", channel="debug")
        # logger.debug(f"Loader type: {type(self.loader)}")
        # logger.debug(f"Loader methods: {dir(self.loader)}")
        # logger.debug(f"Is load_data callable: {callable(getattr(self.loader, 'load_data', None))}")
        try:
            # logger.debug(f"Attempting to call load_data with path: {str(file_path)}, type: {type(file_path)}")
            # logger.debug(f"Extra info: {extra_info}")
            if hasattr(self.loader, 'load_data'):
                # logger.debug(f"Attempting to call load_data with path: {str(file_path)}, extra_info: {extra_info}")
                docs = self.loader.load_data(file_path=str(file_path), extra_info=extra_info)
                # page_content, text = zip(*[(doc.page_content, doc.text) for doc in docs])
                # if page_content == text:
                #     logger.debug(f"page_content and text are the same, skipping indexing")
        except Exception as e:
            logger.debug(f"Error occurred while calling load_data: {e}")
            logger.debug(f"Error type: {type(e)}")
            logger.debug(f"Error args: {e.args}")
            raise

        # logger.debug(f"length of docs: {len(docs)}, docs: {docs}")
        # yield Document(f" => 将 {file_path.name} 转换为文本", channel="debug")
        yield from self.handle_docs(docs, file_id, file_path.name)

        self.finish(file_id, file_path)

        yield Document(f" => 完成索引 {file_path.name}", channel="debug")
        return file_id, docs


class IndexDocumentPipeline(BaseFileIndexIndexing):
    """索引文件。根据文件类型决定使用哪个管道。

    这个方法本质上是一个工厂，用于决定使用哪个索引管道。

    我们可以根据文件类型程序化地决定管道，或者自动地基于LLM决定。
    如果基于LLM，本质上我们会在文件中记录LLM的思考过程，
    然后在索引过程中，我们会读取该文件以决定使用哪个管道，并在该文件中记录操作。
    随着时间的推移，LLM可以学会决定应该使用哪个管道。
    """

    reader_mode: str = Param("default", help="读取模式")
    embedding: BaseEmbeddings
    run_embedding_in_thread: bool = False

    @Param.auto(depends_on="reader_mode")
    def readers(self):
        readers = deepcopy(RAG_DEFAULT_FILE_EXTRACTORS)
        # logger.debug("reader_mode", self.reader_mode)

        dev_readers, _, _ = dev_settings()
        readers.update(dev_readers)

        return readers

    @classmethod
    def get_user_settings(cls):
        return {
            "reader_mode": {
                "name": "文件加载器",
                "value": "default",
                "choices": [
                    ("默认 (开源)", "default"),
                    ("Adobe API (图表+表格提取)", "adobe"),
                    (
                        "Azure AI 文档智能 (图表+表格提取)",
                        "azure-di",
                    ),
                ],
                "component": "dropdown",
            },
        }

    @classmethod
    def get_pipeline(cls, user_settings, index_settings) -> BaseFileIndexIndexing:
        use_quick_index_mode = user_settings.get("quick_index_mode", False)
        # logger.debug("use_quick_index_mode", use_quick_index_mode)

        # 换一种方式读取
        embedding_name = index_settings.get("embedding", embedding_models_manager.get_default_name())
        embedding_model = embedding_models_manager[embedding_name]

        obj = cls(
            embedding=embedding_model,
            run_embedding_in_thread=use_quick_index_mode,
            reader_mode=user_settings.get("reader_mode", "default"),
        )

        return obj

    def route(self, file_path: Path) -> IndexPipeline:
        """根据文件类型决定管道

        可以子类化此方法以实现更复杂的管道路由策略。
        """
        _, chunk_size, chunk_overlap = dev_settings()

        ext = file_path.suffix.lower()
        # reader = self.readers.get(ext, unstructured)
        reader_class = self.readers.get(ext, unstructured)
        # if reader is None:
        if reader_class is None:
            raise NotImplementedError(
                f"没有支持的管道来索引 {file_path.name}。请在设置中指定适合此文件类型的管道。"
            )

        # 动态判断 PDF 加载器
        if ext == ".pdf":
            if is_scanned_pdf(file_path):
                logger.info(f"文件 {file_path} 为扫描 PDF，使用 OCRReader")
                reader_class = OCRReader
            else:
                logger.info(f"文件 {file_path} 为非扫描 PDF，使用 PDFThumbnailReader")
                reader_class = PDFThumbnailReader

        reader = reader_class()
        logger.info(f"使用读取器: {reader}")
        logger.info(f"reader type: {type(reader)}")
        pipeline: IndexPipeline = IndexPipeline(
            loader=reader,
            splitter=RecursiveCharacterSplitter(  # TokenSplitter
                chunk_size=chunk_size or 640,
                chunk_overlap=chunk_overlap or 20,
                # separator="\n\n",
                # backup_separators=["\n", ".", "\u200B"],
            ),
            run_embedding_in_thread=self.run_embedding_in_thread,
            Source=self.Source,
            Index=self.Index,
            VS=self.VS,
            DS=self.DS,
            FSPath=self.FSPath,
            user_id=self.user_id,
            private=self.private,
            embedding=self.embedding,
        )

        # logger.debug(f"pipeline.loader type: {type(pipeline.loader)}")
        return pipeline

    def run(
        self, file_paths: str | Path | list[str | Path], *args, **kwargs
    ) -> tuple[list[str | None], list[str | None]]:
        raise NotImplementedError

    def stream(
        self, file_paths: str | Path | list[str | Path], reindex: bool = False, **kwargs
    ) -> Generator[
        Document, None, tuple[list[str | None], list[str | None], list[Document]]
    ]:
        """返回索引的文件ID列表和错误列表"""
        # logger.debug(f"Indexing file under IndexDocumentPipeline: {file_paths}")
        if not isinstance(file_paths, list):
            file_paths = [file_paths]

        file_ids: list[str | None] = []
        errors: list[str | None] = []
        all_docs = []

        n_files = len(file_paths)
        for idx, file_path in enumerate(file_paths):
            file_path = Path(file_path)
            yield Document(
                content=f"索引 [{idx+1}/{n_files}]: {file_path.name}",
                channel="debug",
            )

            try:
                pipeline = self.route(file_path)
                # logger.debug(f"Indexing file under IndexDocumentPipeline after self.route: {file_path}")
                file_id, docs = yield from pipeline.stream(
                    file_path, reindex=reindex, **kwargs
                )
                # logger.debug(f"Indexing file under IndexDocumentPipeline after yield from pipeline.stream: {file_path}")
                all_docs.extend(docs)
                file_ids.append(file_id)
                errors.append(None)
                yield Document(
                    content={"file_path": file_path, "status": "success"},
                    channel="index",
                )
            except Exception as e:
                logger.exception(e)
                file_ids.append(None)
                errors.append(str(e))
                yield Document(
                    content={
                        "file_path": file_path,
                        "status": "failed",
                        "message": str(e),
                    },
                    channel="index",
                )

        return file_ids, errors, all_docs
