from typing import Optional

from API.db.engine import sync_engine
from sqlalchemy import <PERSON><PERSON><PERSON>, Column
from sqlmodel import Field, SQLModel


# TODO: 使用SQLAlchemy直接简化
class Index(SQLModel, table=True):
    __table_args__ = {"extend_existing": True}
    __tablename__ = "API__index"  # type: ignore

    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(unique=True)
    index_type: str = Field()
    config: dict = Field(default={}, sa_column=Column(JSON))


Index.metadata.create_all(sync_engine)
