import threading
import asyncio
import logging
from typing import Optional, Type
from fastapi import BackgroundTasks

from API.db.models import sync_engine, async_engine
from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlalchemy.future import select
from theflow.settings import settings
from theflow.utils.modules import import_dotted_string

from API.index.base import BaseIndex
from API.index.model import Index

logger = logging.getLogger(__name__)


class IndexManager:
    """管理应用程序索引

    索引管理器负责：
        - 管理可能的索引范围及其扩展
        - 每个由用户构建的实际索引

    属性：
        - indices: 用户构建的索引列表
    """

    _initialized = False  # 用于判断是否已经执行过启动逻辑

    def __init__(self, app):
        self._app = app
        self._indices = []
        self._index_types: dict[str, Type[BaseIndex]] = {}
        self._lock = threading.Lock()  # 线程锁
        self._index_configs = {}  # 存储索引配置

    @property
    def index_types(self) -> dict:
        """列出索引的类型"""
        return self._index_types

    def build_index(self, name: str, config: dict, index_type: str):
        """构建索引

        构建索引意味着将索引信息记录到数据库中并返回索引对象。

        参数：
            name (str): 索引的名称
            config (dict): 索引的配置
            index_type (str): 索引的类型
            id (int, optional): 索引的ID。如果为None，ID将自动生成。默认为None。

        返回：
            BaseIndex: 索引对象
        """
        logger.info(f"start build index")
        with Session(sync_engine) as sess:
            if self.exists(name=name):
                raise ValueError(f'索引 "{name}" 已经存在')

            entry = Index(name=name, config=config, index_type=index_type)
            sess.add(entry)

            try:
                sess.commit()
                sess.refresh(entry)
            except Exception as e:
                sess.rollback()  # 如果失败，回滚事务
                raise ValueError(f'无法创建索引 "{name}": {e}')

            try:
                # 构建索引
                index_cls = import_dotted_string(index_type, safe=False)
                index = index_cls(app=self._app, id=entry.id, name=name, config=config)
                index.on_create()

                # 更新条目
                entry.config = index.config
                sess.commit()
            except Exception as e:
                sess.rollback()
                sess.delete(entry)
                sess.commit()
                raise ValueError(f'无法创建索引 "{name}": {e}')

        return index

    async def update_index(self, id: int, name: str, config: dict):
        """更新索引信息

        参数：
            id: 索引的ID
            name: 索引的新名称
            config: 索引的新配置
        """
        async with AsyncSession(async_engine, expire_on_commit=False) as sess:
            entry = await sess.get(Index, id)
            if entry is None:
                raise ValueError(f"ID为 {id} 的索引不存在")

            entry.name = name
            entry.config = config
            await sess.commit()

        for index in self._indices:
            if index.id == id:
                index.name = name
                index.config = config
                break

    def start_index(self, id: int, name: str, config: dict, index_type: str,
                    background_tasks: Optional[BackgroundTasks] = None):
        """启动索引

        参数：
            id (int): 索引的ID
            name (str): 索引的名称
            config (dict): 索引的配置
            index_type (str): 索引的类型
        """
        index_cls = import_dotted_string(index_type, safe=False)
        index = index_cls(app=self._app, id=id, name=name, config=config)

        if background_tasks:
            # 使用后台任务启动索引，避免阻塞
            background_tasks.add_task(index.on_start)
        else:
            index.on_start()

        self._indices.append(index)
        return index

    async def delete_index(self, id: int):
        """从数据库中删除索引"""
        with self._lock:  # 确保同一时间只有一个线程在操作
            index: Optional[BaseIndex] = None
            for _ in self._indices:
                if _.id == id:
                    index = _
                    break

            if index is None:
                raise ValueError(
                    "索引不存在。如果你已经删除了索引，请重启以反映更改。"
                )

            try:
                try:
                    # 清理
                    await index.on_delete()
                except Exception as e:
                    logger.info(f"删除索引 {index.name} 时出错: {e}")

                # 从数据库中移除
                async with AsyncSession(async_engine, expire_on_commit=False) as sess:
                    item = await sess.get(Index, id)
                    await sess.delete(item)
                    await sess.commit()

                new_indices = [_ for _ in self._indices if _.id != id]
                self._indices = new_indices
            except Exception as e:
                raise ValueError(f"无法删除索引 {index.name}: {e}")

    def load_index_types(self):
        """加载支持的索引类型"""
        self._index_types = {}

        # 内置索引类型
        from API.index.file import FileIndex

        for index in [FileIndex]:
            self._index_types[f"{index.__module__}.{index.__qualname__}"] = index

        # 开发者定义的自定义索引类型
        for index_str in settings.RAG_INDEX_TYPES:
            cls: Type[BaseIndex] = import_dotted_string(index_str, safe=False)
            self._index_types[f"{cls.__module__}.{cls.__qualname__}"] = cls

    def exists(self, id: Optional[int] = None, name: Optional[str] = None) -> bool:
        """检查索引是否存在

        参数：
            id (int): 索引的ID

        返回：
            bool: 如果索引存在，返回True，否则返回False
        """
        if id:
            with Session(sync_engine, expire_on_commit=False) as sess:
                index = sess.get(Index, id)
                return index is not None

        if name:
            with Session(sync_engine, expire_on_commit=False) as sess:
                result = sess.execute(select(Index).where(Index.name == name))
                index = result.one_or_none()
                return index is not None

        return False

    def on_application_startup(self):
        """当应用程序启动时，由基础应用程序调用此方法

        从数据库加载索引
        """
        if not self.__class__._initialized:
            logging.info(f"初始化程序")
            # 仅在第一次启动时执行初始化逻辑
            self.load_index_types()

            for index in settings.RAG_INDICES:
                if not self.exists(name=index["name"]):
                    self.build_index(**index)

            with Session(sync_engine) as sess:
                index_defs = sess.exec(select(Index))
                for index_def in index_defs:
                    # 由于 index_def 是一个 tuple，提取其中的 Index 实例
                    index_instance = index_def[0]

                    # 过滤掉内部的SQLAlchemy属性
                    index_data = {
                        key: value for key, value in index_instance.__dict__.items()
                        if not key.startswith("_sa_")
                    }
                    self.start_index(**index_data)

            self.__class__._initialized = True
            logging.info("完成初始化")

        else:
            logging.info("IndexManager 已经初始化，跳过重复启动逻辑。")

    def on_application_startup_sync(self):
        """同步版本的启动逻辑"""
        try:
            # 尝试获取当前的事件循环
            loop = asyncio.get_running_loop()
        except RuntimeError:
            # 如果当前没有事件循环，则创建一个新的
            loop = None

        if loop and loop.is_running():
            logging.info("Event loop is running, using run_coroutine_threadsafe.")
            # 如果事件循环正在运行，使用 `run_coroutine_threadsafe` 在已有的事件循环中执行异步任务
            task = loop.create_task(self.on_application_startup())
            # 如果需要等待任务完成，可以考虑注册回调函数而不是阻塞
            task.add_done_callback(lambda future: logging.info("Async startup task completed."))
        else:
            logging.info("No running loop, using asyncio.run to execute startup.")
            # 如果没有运行的事件循环，可以直接运行
            asyncio.run(self.on_application_startup())
            logging.info("Async startup completed.")

    @property
    def indices(self):
        return self._indices

    async def get_index_id_by_name(self, index_name: str) -> int:
        """根据索引名称获取对应的ID
        
        参数:
            index_name: 索引名称,如'FileIndex'或'GraphRAG'
            
        返回:
            索引ID
        """
        # 处理名称,去掉"Index"后缀
        if index_name.endswith("Index"):
            index_name = index_name[:-5]
            
        async with AsyncSession(async_engine) as sess:
            statement = select(Index).where(Index.name == index_name)
            result = await sess.exec(statement)
            index = result.scalar_one_or_none()
            
            if not index:
                raise ValueError(f"索引 {index_name} 配置不存在")
                
            return index.id
            
    async def create_task_index(self, name: str, config: dict, index_name: str = "FileIndex") -> BaseIndex:
        """为任务创建新的索引实例，但复用已有的数据库表
        
        参数:
            name: 索引名称
            config: 索引配置
            index_name: 索引名称,默认为'FileIndex'
        """
        try:
            # 获取对应索���的ID
            index_id = await self.get_index_id_by_name(index_name)
            
            # 获取索引配置
            async with AsyncSession(async_engine) as sess:
                statement = select(Index).where(Index.id == index_id)
                result = await sess.exec(statement)
                index_entry = result.scalar_one_or_none()
                
                if not index_entry:
                    raise ValueError(f"索引 {index_name} 配置不存在")
                
                base_config = index_entry.config
                # 合并配置
                merged_config = {**base_config, **config}
                
            # 创建新的索引实例,但使用相同的id
            index_cls = import_dotted_string(index_entry.index_type, safe=False)
            index = index_cls(
                app=self._app,
                id=index_id,  # 使用查询到的id
                name=name,
                config=merged_config
            )
            
            # 只初始化必要的组件,不创建新表
            await self._init_task_index(index)
            
            return index
            
        except Exception as e:
            logger.error(f"创建任务索引失败: {e}")
            raise ValueError(f"无法创建任务索引: {e}")

    async def _init_task_index(self, index: BaseIndex):
        """初始化任务索引的必要组件
        
        参数:
            index: 索引实例
        """
        try:
            # 设置必要的资源和组件
            index._setup_resources()
            index._setup_indexing_cls()
            index._setup_retriever_cls()
            
            # 不需要创建新表，因为我们复用已有的表
            logger.info(f"任务索引 {index.name} 初始化完成")
            
        except Exception as e:
            logger.error(f"初始化任务索引 {index.name} 失败: {e}")
            raise ValueError(f"初始化任务索引失败: {e}")