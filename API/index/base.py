import abc
import logging
from typing import TYPE_CHECKING, Any, Optional

if TYPE_CHECKING:
    from API.app import BasePage

    from RAG.base import BaseComponent


logger = logging.getLogger(__name__)


class BaseIndex(abc.ABC):
    """索引的基类

    索引负责以可搜索的方式存储信息，并检索这些信息。

    一个应用程序可以有多个索引。例如：
        - 计算机本地文件的索引
        - Discord、Slack等聊天消息的索引
        - Google Drive、Dropbox等存储文件的索引
        - ...

    用户可以在这个应用程序中创建、删除和管理索引。他们可以创建一个索引，设置它来跟踪计算机中的本地文件夹，
    然后聊天机器人可以在该文件夹中搜索文件。用户可以创建另一个索引来跟踪他们在Discord上的聊天消息，等等。

    这个类定义了索引的接口。它涉及：
        - 为索引工作设置必要的软件基础设施（例如数据库表、向量存储集合等）。
        - 提供用户与索引交互的UI，包括设置。

    方法：

        __init__: 初始化索引工作所需的任何资源定义（例如数据库表、向量存储集合等）。
        on_create: 仅在用户创建索引时调用一次。
        on_delete: 仅在用户删除索引时调用一次。
        on_start: 在索引启动时调用。
        get_admin_settings: 返回管理员设置。仅在用户创建索引时调用（供管理员自定义）。输出将存储在索引的配置中。
        get_indexing_pipeline: 当实体被填充到索引中时返回索引管道。
        get_retriever_pipelines: 当用户聊天时返回检索管道。
    """

    def __init__(self, app, id, name, config):
        self._app = app
        self.id = id
        self.name = name
        self.config = config  # 管理员设置

    def on_create(self):
        """首次创建索引"""

    def on_delete(self):
        """当用户删除索引时触发"""

    def on_start(self):
        """当索引启动时触发

        参数：
            id (int): 索引的ID
            name (str): 索引的名称
            config (dict): 索引的配置
        """

    @classmethod
    def get_admin_settings(cls) -> dict:
        """返回默认的管理员设置。这些是构建时的设置。

        设置将在管理员设置页面中填充。并在初始化索引和检索管道时使用。

        返回：
            dict: 用户设置，格式为 `API.settings.SettingItem` 的字典
        """
        return {}

    @abc.abstractmethod
    def get_indexing_pipeline(
        self, settings: dict, user_id: Optional[int]
    ) -> "BaseComponent":
        """返回将实体填充到索引中的索引管道

        参数：
            settings: 索引的用户设置
            user_id: 访问索引的用户ID
                TODO: 应该有一个app_state，而不是user_id，其中可能还包含设置。

        返回：
            BaseIndexing: 索引管道
        """
        ...

    # def get_retriever_pipelines(
    #     self, settings: dict, user_id: int, selected: Any = None
    # ) -> list["BaseComponent"]:
    #     """返回从索引中检索实体的检索管道"""
    #     return []

    def get_retriever_pipelines(
        self, settings: dict, user_id: int
    ) -> list["BaseComponent"]:
        """返回从索引中检索实体的检索管道"""
        return []

