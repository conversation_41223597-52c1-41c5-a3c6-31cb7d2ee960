from pathlib import Path
from datetime import datetime
import csv
import asyncio
import logging
from filelock import FileLock
from sqlmodel import select

from copy import deepcopy
from sqlalchemy.ext.asyncio import AsyncSession
from API.pages.chat.conv_service import ConvService
from API.db.models import Conversation, async_engine, sync_engine
from API.components import reasonings
from API.pages.chat.common import STATE

from RAG.base import Document

DEFAULT_SETTING = "(default)"
logger = logging.getLogger(__name__)


class ChatService:
    def __init__(self, app, conv_service: ConvService):
        self._app = app
        self._conv_service = conv_service
        self.last_file_update_time = None  # 初始化文件更新的时间戳

    async def submit_msg(self, chat_input, chat_history, user_id, conv_id, conv_name):
        if not chat_input:
            raise ValueError("输入为空")

        if not conv_id:
            # 如果没有会话ID，则创建新的会话
            id_, update = await self._create_new_conversation(user_id)
            async with AsyncSession(async_engine) as session:
                statement = select(Conversation).where(Conversation.id == id_)
                # result = await session.exec(statement)  # 改为异步执行查询
                # name = (await result.one()).name  # 确保查询是异步的
                result = await session.execute(statement)  # 修改为 execute
                conversation = result.scalars().one()  # 使用 scalars().one() 提取单个结果
                name = conversation.name
                new_conv_id = id_
                conv_update = update
                new_conv_name = name
        else:
            new_conv_id = conv_id
            conv_update = None  # 这里使用None替代gr.update()
            new_conv_name = conv_name

        return {
            "input": "",  # 清空输入框
            "chat_history": chat_history + [(chat_input, None)],  # 更新聊天记录
            "conv_id": new_conv_id,  # 返回新会话ID
            "conv_update": conv_update,  # 更新会话信息
            "conv_name": new_conv_name,  # 更新会话名称
        }

    async def persist_data_source(
            self,
            convo_id,
            user_id,
            retrieval_msg,
            plot_data,
            retrieval_history,
            plot_history,
            messages,
            state,
            *selecteds):
        if not convo_id:
            return {"error": "未选择会话"}

        try:
            # 将新消息追加到历史中，更新再生状态
            if not state["app"].get("regen", False):
                retrieval_history.append(retrieval_msg)
                plot_history.append(plot_data)
            else:
                if retrieval_history:
                    retrieval_history[-1] = retrieval_msg
                    plot_history[-1] = plot_data

            # 重置再生状态
            state["app"]["regen"] = False

            # 处理选中的文件信息
            selecteds_ = {}
            for index in self._app.index_manager.indices:
                if index.selector is not None:
                    selecteds_[str(index.id)] = (
                        selecteds[index.selector]
                        if isinstance(index.selector, int)
                        else [selecteds[i] for i in index.selector]
                    )

            # 更新数据库中的会话记录
            async with AsyncSession(async_engine) as session:
                statement = select(Conversation).where(Conversation.id == convo_id)
                # result = await session.exec(statement)  # 改为异步执行查询
                # conversation = await result.one()  # 获取查询结果
                result = await session.execute(statement)
                conversation = result.scalars().one()
                # is_owner = result.user == user_id
                # data_source = result.data_source
                is_owner = conversation.user == user_id
                data_source = conversation.data_source

                result.data_source = {
                    "selected": selecteds_ if is_owner else data_source.get("selected", {}),
                    "messages": messages,
                    "retrieval_messages": retrieval_history,
                    "plot_history": plot_history,
                    "state": state,
                    "likes": deepcopy(data_source.get("likes", [])),
                }
                session.add(result)
                await session.commit()

            return {"retrieval_history": retrieval_history, "plot_history": plot_history}
        except Exception as e:
            print(f"Error in persist_data_source: {e}")
            return {"error": str(e)}

    def create_pipeline(self, settings, session_reasoning_type, session_llm, state, user_id, *selecteds):
        """创建推理管道，基于应用设置

                参数:
                    settings: 整体设定
                    state: 整体状态
                    selected: 所需文档id的列如果没有，就是所有文档。

                输出:
                    - 管道对象
                """
        # 针对临时对话页面重写推理模式
        # print(f"Session reasoning type: {session_reasoning_type}")
        # print(f"Session LLM: {session_llm}")

        reasoning_mode = (
            settings["reasoning.use"]
            if session_reasoning_type in (DEFAULT_SETTING, None)
            else session_reasoning_type
        )
        reasoning_cls = reasonings[reasoning_mode]
        # print("Reasoning class", reasoning_cls)
        reasoning_id = reasoning_cls.get_info()["id"]

        settings = deepcopy(settings)
        llm_setting_key = f"reasoning.options.{reasoning_id}.llm"
        if llm_setting_key in settings and session_llm not in (DEFAULT_SETTING, None):
            settings[llm_setting_key] = session_llm
        settings[llm_setting_key] = session_llm

        # 获得检索器
        retrievers = []
        for index in self._app.index_manager.indices:
            # index_selected = []
            # if isinstance(index.selector, int):
            #     index_selected = selecteds[index.selector]
            # if isinstance(index.selector, tuple):
            #     for i in index.selector:
            #         index_selected.append(selecteds[i])
            # iretrievers = index.get_retriever_pipelines(
            #     settings, user_id, index_selected
            # )
            iretrievers = index.get_retriever_pipelines(settings, user_id)
            retrievers += iretrievers

        # 准备状态
        reasoning_state = {
            "app": deepcopy(state["app"]),
            "pipeline": deepcopy(state.get(reasoning_id, {})),
        }

        pipeline = reasoning_cls.get_pipeline(settings, reasoning_state, retrievers)

        return pipeline, reasoning_state

    async def get_task_pipeline(self, settings, state, user_id):
            """为每个任务创建独立的pipeline实例"""
            pipeline, reasoning_state = self.create_pipeline(
                settings,
                session_reasoning_type=DEFAULT_SETTING,
                session_llm=DEFAULT_SETTING,
                state=state,
                user_id=user_id
            )
            return pipeline, reasoning_state

    async def chat_fn(self, conversation_id, chat_history, settings, reasoning_type,
            llm_type, state, user_id, *selecteds):
        chat_input = chat_history[-1][0]
        chat_history = chat_history[:-1]

        # 为当前任务创建独立的pipeline
        pipeline, reasoning_state = await self.get_task_pipeline(
            settings, state, user_id
        )

        text, refs, plot = "", "", None
        msg_placeholder = "思考中..."  # 使用配置或默认值
        response_count = 0
        has_yielded_content = False

        try:
            logger.info(f"开始处理问题: '{chat_input}'")
            # async for response in pipeline.stream(chat_input, conversation_id, chat_history):
            # logger.info("开始调用pipeline.stream并将其转换为异步生成器")
            stream = pipeline.stream(chat_input, conversation_id, chat_history)
            # logger.info(f"获取到stream对象: {stream}")
            
            async for response in self._to_async_generator(stream):
                response_count += 1
                # logger.info(f"收到响应 #{response_count} 类型: {type(response)}")
                
                # 检查响应是否有内容
                has_content = False
                if isinstance(response, Document):
                    response_content = response.content
                    # logger.info(f"处理Document响应: channel={response.channel}, content类型={type(response_content)}")
                    has_content = response_content is not None and (not isinstance(response_content, str) or response_content.strip())
                elif isinstance(response, str):
                    response_content = response
                    has_content = response_content.strip() != ""
                else:
                    response_content = str(response) if response is not None else ""
                    has_content = response_content.strip() != ""
                
                # logger.debug(f"响应内容: {response_content if isinstance(response_content, str) else '(非字符串内容)'}")
                
                if not has_content:
                    logger.warning(f"收到空响应，跳过: {response}")
                    continue
                
                has_yielded_content = True
                
                if isinstance(response, Document):
                    yield response
                else:
                    # 将非Document响应转换为Document
                    try:
                        # logger.info(f"将非Document响应转换为Document: {response}")
                        doc = Document(channel="chat", content=response_content)
                        yield doc
                    except Exception as e:
                        logger.error(f"转换响应到Document时出错: {str(e)}")
                        logger.error(f"Error type: {type(e)}")
                        # 尝试直接返回字符串
                        yield response
                    
            # 如果没有生成任何有内容的响应，提供一个默认响应
            if not has_yielded_content:
                logger.warning("整个过程没有产生任何有内容的响应")
                default_response = Document(channel="chat", content="抱歉，处理您的请求时没有获得有效的响应。")
                yield default_response
                
        except Exception as e:
            logger.error(f"Error in chat_fn: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            # 在发生异常时也提供一个响应
            yield Document(channel="chat", content=f"处理您的请求时发生错误: {str(e)}")
            raise

        logger.debug(f"chat_fn结束，共产生了{response_count}个响应")

    async def _to_async_generator(self, sync_gen):
        """
        将同步生成器转换为异步生成器
        """
        item_count = 0
        try:
            for item in sync_gen:
                item_count += 1
                if item is None:
                    logger.warning("同步生成器产生了None项，跳过")
                    continue
                    
                # logger.info(f"同步生成器产生项目 #{item_count}: 类型={type(item)}")
                if isinstance(item, str) and not item.strip():
                    logger.warning("同步生成器产生了空字符串")
                    # 不跳过空字符串，让上层决定如何处理
                    
                await asyncio.sleep(0)  # 确保异步行为，避免阻塞
                yield item
                
            if item_count == 0:
                logger.warning("同步生成器没有产生任何项目")
                # 不生成默认项，让上层决定如何处理
        except Exception as e:
            logger.error(f"_to_async_generator出错: {str(e)}")
            raise

    async def save_log(
            self,
            conversation_id,
            chat_history,
            settings,
            info_panel,
            original_chat_history,
            original_settings,
            original_info_panel,
            log_dir):
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
        log_file = log_path / f"{datetime.now():%Y%m%d_%H}_log.csv"

        async with FileLock(str(log_file) + ".lock"):
            with open(log_file, "a", newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                if not log_file.exists():
                    writer.writerow([
                        "会话ID", "消息ID", "问题", "回答", "聊天记录", "设置信息", "证据", "反馈",
                        "是否原始", "原始回答", "原始聊天记录", "原始设置信息", "原始证据",
                    ])

                # async with Session(engine) as session:
                async with AsyncSession(async_engine) as session:
                    convo = await session.get(Conversation, conversation_id)
                    if not convo or not convo.data_source.get("likes"):
                        return

                    feedback = convo.data_source["likes"][-1][-1]
                    message_index = convo.data_source["likes"][-1][0]
                    current_message = chat_history[message_index[0]]
                    original_message = original_chat_history[message_index[0]]
                    is_original = current_message == original_message

                    writer.writerow([
                        conversation_id, message_index, current_message[0], current_message[1],
                        chat_history, settings, info_panel, feedback,
                        is_original, original_message[1], original_chat_history,
                        original_settings, original_info_panel
                    ])

    def _create_new_conversation(self, user_id):
        return self._conv_service.new_conv(user_id)
