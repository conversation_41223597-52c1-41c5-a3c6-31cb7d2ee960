from typing import Type

from theflow.settings import settings as flowsettings
from theflow.utils.modules import deserialize

from RAG.embeddings.base import BaseEmbeddings


class EmbeddingManager:
    """代表模型池"""

    def __init__(self):
        self._models: dict[str, BaseEmbeddings] = {}
        self._info: dict[str, dict] = {}
        self._default: str = ""
        self._vendors: list[Type] = []

        if hasattr(flowsettings, "RAG_EMBEDDINGS"):
            # 直接从设置中加载
            for name, model in flowsettings.RAG_EMBEDDINGS.items():
                # 动态传递模型参数（例如 model_name）
                # 先反序列化实例对象
                model_instance = deserialize(model["spec"], safe=False)

                # 从 spec 中提取参数，设置到 model_instance
                if hasattr(model_instance, 'model_name'):
                    model_instance.model_name = model["spec"].get("model_name")
                if hasattr(model_instance, 'local_model_path'):
                    model_instance.local_model_path = model["spec"].get("local_model_path")

                self._models[name] = model_instance
                self._info[name] = {
                    "name": name,
                    "spec": model["spec"],
                    "default": model.get("default", False)
                }
                if model.get("default", False):
                    self._default = name
            self.load_vendors()
        # print(f"complete embedding init with self._models: {self._models}, self._info: {self._info}, self._default: {self._default}")

    def options(self) -> dict:
        """显示模型的字典"""
        return self._models

    def load_vendors(self):
        from RAG.embeddings import \
            AzureOpenAIEmbeddings, \
            FastEmbedEmbeddings, \
            LCHuggingFaceEmbeddings, \
            OpenAIEmbeddings, \
            STEmbeddings

        self._vendors = [AzureOpenAIEmbeddings,
                         OpenAIEmbeddings,
                         FastEmbedEmbeddings,
                         LCHuggingFaceEmbeddings,
                         STEmbeddings]
        # print(f"Loaded embedding vendors: {self._vendors}")

    def get_default_name(self) -> str:
        """获取默认模型的名称

        如果没有默认模型，则从池中选择随机模型。 如果有多个默认模型，则从中随机选择。

        返回:
            字符串: 模型名称
        """
        if not self._models:
            raise ValueError("没有可选模型")

        # if not self._default:
        #     return self.get_random_name()

        return self._default

    def __getitem__(self, key):
        """允许使用方括号访问模型"""
        if key == "default":
            return self._models[self._default]
        return self._models[key]


embedding_models_manager = EmbeddingManager()
