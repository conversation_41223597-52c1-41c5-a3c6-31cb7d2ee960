from typing import Type

from theflow.settings import settings as flowsettings
from theflow.utils.modules import deserialize, import_dotted_string

from RAG.llms import ChatLLM


class LLMManager:
    """代表模型池"""

    def __init__(self):
        self._models: dict[str, ChatLLM] = {}
        self._info: dict[str, dict] = {}
        self._default: str = ""
        self._vendors: list[Type] = []

        # 从flowsettings配置中加载模型
        if hasattr(flowsettings, "RAG_LLMS"):
            for name, model in flowsettings.RAG_LLMS.items():
                self._models[name] = deserialize(model["spec"], safe=False)
                self._info[name] = {"name": name, "spec": model["spec"], "default": model.get("default", False)}
                if model.get("default", False):
                    self._default = name
            self.load_vendors()

    def get(self, name: str, default=None) -> ChatLLM:
        """根据名称获取模型，如果未找到则返回默认值"""
        return self._models.get(name, default)

    def get_default(self) -> ChatLLM:
        """获取默认模型"""
        if self._default:
            return self._models[self._default]
        else:
            raise ValueError("没有设置默认模型")

    def load_vendors(self):
        from RAG.llms import AzureChatOpenAI, ChatOpenAI, LCAnthropicChat, LlamaCppChat

        self._vendors = [ChatOpenAI, AzureChatOpenAI, LCAnthropicChat, LlamaCppChat]

        # 如果设置中指定，动态添加额外的供应商
        for extra_vendor in getattr(flowsettings, "RAG_LLM_EXTRA_VENDORS", []):
            self._vendors.append(import_dotted_string(extra_vendor, safe=False))

    def options(self) -> dict:
        """呈现模型的字典"""
        return self._models


llms = LLMManager()
