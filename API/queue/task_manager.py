from datetime import datetime
import uuid
import asyncio
from typing import Optional, Dict, Any
import json
from logging import getLogger
from enum import Enum
from .thread_pools import ThreadPoolManager

logger = getLogger(__name__)


class TaskStatus:
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskType(Enum):
    CHAT = "chat"
    FILE = "file"


class TaskManager:
    """任务管理器"""

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self._last_status = {}  # 用于缓存上一次的状态
        self.thread_pool_manager = ThreadPoolManager()
        self._workers = {}  # 存储任务ID和对应的worker实例
        self._worker_events = {}  # 存储每个任务的事件
        self._task_status_events = {}  # 新增：用于追踪任务状态变化的事件
        logger.info("TaskManager initialized with Redis client and ThreadPoolManager.")

    def create_task(self, task_type: str, task_data: dict, log_create: bool = True) -> str:
        """
        创建并启动任务

        Args:
            task_type: 任务类型 (如 'file', 'chat')
            task_data: 任务数据

        Returns:
            task_id: 任务ID
        """
        logger.info(f"Creating task of type '{task_type}' with data: {task_data}")
        # 1. 生成任务ID和任务信息
        task_id = str(uuid.uuid4())
        task_info = {
            'task_id': task_id,
            'type': task_type,
            'status': TaskStatus.PENDING,
            'created_at': datetime.now().isoformat(),
            'data': json.dumps(task_data),  # 序列化任务数据
            'result': '',
            'error': ''
        }
        logger.debug(f"Generated task ID: {task_id} with initial info: {task_info}")

        # 2. 存储任务信息
        try:
            self.redis_client.hset(f'task:{task_id}', mapping=task_info)
            if log_create:
                logger.info(f"Created task {task_id} of type {task_type}")
        except Exception as e:
            logger.error(f"Failed to store task info: {e}")
            raise

        # 3. 提交任务到线程池
        try:
            self.thread_pool_manager.submit_task(task_type=task_type, callback=self._task_callback, task_id=task_id, task_data=task_data)
            logger.info(f"Task {task_id} submitted to ThreadPoolManager.")

        except Exception as e:
            self.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error=f"Failed to submit task: {str(e)}"
            )
            logger.error(f"Failed to submit task {task_id} to ThreadPoolManager: {e}")
            raise

        return task_id

    def get_task_status(self, task_id: str, log_status: bool = False) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        Args:
            task_id: 任务ID
            log_status: 是否记录状态日志，默认False
        """
        try:
            task_info = self.redis_client.hgetall(f'task:{task_id}')
            if not task_info:
                return None

            # 将bytes转换为字符串
            task_info = {k.decode('utf-8'): v.decode('utf-8') for k, v in task_info.items()}
            
            # 只有当状态发生变化时才记录日志
            current_status = task_info.get('status')
            if log_status and self._last_status.get(task_id) != current_status:
                logger.debug(f"Task {task_id} status: {current_status}")
                self._last_status[task_id] = current_status

            return task_info

        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {e}")
            return None

    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Returns:
            bool: 是否成功取消
        """
        logger.debug(f"Attempting to cancel task {task_id}.")
        task_info = self.get_task_status(task_id)
        if not task_info:
            logger.warning(f"Task {task_id} not found. Cannot cancel.")
            return False

        if task_info['status'] in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
            logger.info(f"Task {task_id} is already {task_info.get('status')}. Cannot cancel.")
            return False

        # 尝试从线程池取消任务
        cancelled = self.thread_pool_manager.cancel_task(task_id)
        if cancelled:
            self.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error="Task cancelled by user"
            )
            logger.info(f"Task {task_id} has been cancelled.")
        else:
            logger.warning(f"Failed to cancel task {task_id}. It may have already started.")
        return cancelled

    def update_task_status(self, task_id: str, status: str, result: Any = None, error: str = None, log_update: bool = True) -> None:
        """
        更新任务状态
        Args:
            task_id: 任务ID
            status: 新状态
            result: 任务结果
            error: 错误信息
            log_update: 是否记录更新日志
        """
        try:
            update_dict = {
                'status': status,
                'updated_at': datetime.now().isoformat(),
            }

            if result is not None:
                if isinstance(result, (dict, list)):
                    # logger.debug(f"Updating task {task_id} with result: {result}")
                    update_dict['result'] = json.dumps(result)
                else:
                    update_dict['result'] = json.dumps(str(result))
                    
            if error is not None:
                update_dict['error'] = error

            self.redis_client.hset(f'task:{task_id}', mapping=update_dict)
            
            if log_update:
                logger.info(f"Task {task_id} status updated to '{status}'.")
                
        except Exception as e:
            logger.error(f"Failed to update task {task_id}: {e}")
            raise

    def _task_callback(
            self,
            task_id: str,
            success: bool,
            result: Optional[Dict] = None,
            error: Optional[str] = None
    ):
        """任务完成回调"""
        status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        logger.debug(f"Callback for task {task_id}: success={success}, result={result}, error={error}")
        self.update_task_status(task_id, status, result, error)

    def register_worker(self, task_id: str, worker: Any) -> None:
        """注册worker并触发事件"""
        logger.info(f"Registering worker for task {task_id}")
        self._workers[task_id] = worker
        
        # 如果存在等待的事件，设置它
        if task_id in self._worker_events:
            self._worker_events[task_id].set()

    async def wait_for_worker(self, task_id: str) -> Any:
        """等待worker注册完成，不设置超时"""
        if task_id in self._workers:
            return self._workers[task_id]

        # 创建事件
        event = asyncio.Event()
        self._worker_events[task_id] = event
        
        try:
            while True:
                # 检查任务状态
                task_info = self.get_task_status(task_id)
                if not task_info:
                    raise ValueError(f"Task {task_id} not found")
                
                status = task_info.get('status')
                if status == TaskStatus.FAILED:
                    raise ValueError(f"Task {task_id} failed before worker registration")
                
                if task_id in self._workers:
                    return self._workers[task_id]
                
                # 如果任务在等待中，每5秒记录一次日志
                if status == TaskStatus.PENDING:
                    logger.info(f"Task {task_id} is pending in queue...")
                    await asyncio.sleep(5)
                    continue
                
                # 如果任务正在运行但还没有worker，等待短暂时间后重试
                if status == TaskStatus.RUNNING:
                    await asyncio.sleep(0.1)
                    continue

        except Exception as e:
            logger.error(f"Error while waiting for worker {task_id}: {str(e)}")
            raise
        finally:
            # 清理事件
            self._worker_events.pop(task_id, None)

    def remove_worker(self, task_id: str) -> None:
        """移除worker和相关事件"""
        if task_id in self._workers:
            logger.debug(f"Removing worker for task {task_id}")
            del self._workers[task_id]
        
        # 清理相关事件
        if task_id in self._worker_events:
            self._worker_events.pop(task_id)

    async def wait_for_task_completion(self, task_id: str) -> Dict:
        """等待任务完成并返回结果"""
        while True:
            task_info = self.get_task_status(task_id)
            if not task_info:
                raise ValueError(f"Task {task_id} not found")
            
            status = task_info.get('status')
            if status == TaskStatus.COMPLETED:
                return {"status": "completed", "result": task_info.get('result')}
            elif status == TaskStatus.FAILED:
                return {"status": "failed", "error": task_info.get('error')}
            
            await asyncio.sleep(0.1)