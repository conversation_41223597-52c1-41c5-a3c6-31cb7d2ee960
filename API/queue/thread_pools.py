import threading
import logging
import async<PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor, Future
from typing import Dict, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
import flowsettings
import psutil
import time

logger = logging.getLogger(__name__)


@dataclass
class ThreadPoolStats:
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    queue_size: int = 0
    active_threads: int = 0
    avg_task_time: float = 0.0
    pool_utilization: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'queue_size': self.queue_size,
            'active_threads': self.active_threads,
            'avg_task_time': self.avg_task_time,
            'pool_utilization': self.pool_utilization,
            'last_update': self.last_update.isoformat()
        }


class MonitoredThreadPoolExecutor(ThreadPoolExecutor):
    """可监控的线程池执行器"""

    def __init__(self, name: str, min_workers: int, max_workers: int, *args, **kwargs):
        super().__init__(max_workers=max_workers, *args, **kwargs)
        self.name = name
        self.task_times: Dict[int, float] = {}
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.total_tasks = 0
        self.start_time = time.time()
        self._lock = threading.Lock()
        self.min_workers = min_workers
        self.max_workers = max_workers

        # 创建一个新的线程列表来跟踪
        self._active_threads = set()
        # 确保最小数量的线程
        self._ensure_min_workers()
        logger.info(
            f"MonitoredThreadPoolExecutor '{self.name}' initialized with min_workers={self.min_workers}, "
            f"max_workers={self.max_workers}")

    def submit(self, fn, *args, **kwargs):
        """提交任务并记录开始时间"""
        with self._lock:
            self.total_tasks += 1
        task_id = id(fn)

        def wrapped_fn(*args, **kwargs):
            start_time = time.time()
            try:
                result = fn(*args, **kwargs)
                with self._lock:
                    self.completed_tasks += 1
                return result
            except Exception as e:
                with self._lock:
                    self.failed_tasks += 1
                logger.error(f"Task failed in pool {self.name}: {str(e)}")
                raise
            finally:
                end_time = time.time()
                duration = end_time - start_time
                # 记录当前任务的执行耗时，以便后续计算平均任务时间
                with self._lock:
                    self.task_times[task_id] = duration

        future = super().submit(wrapped_fn, *args, **kwargs)
        return future

    def get_stats(self) -> ThreadPoolStats:
        """获取线程池统计信息"""
        with self._lock:
            active_threads = len([t for t in self._threads if t.is_alive()])
            queue_size = self._work_queue.qsize()

            # 计算平均任务时间
            total_completed = self.completed_tasks + self.failed_tasks
            if total_completed > 0:
                avg_task_time = sum(self.task_times.values()) / total_completed
            else:
                avg_task_time = 0.0

            # 计算线程池利用率
            pool_utilization = active_threads / self.max_workers if self.max_workers > 0 else 0.0

            stats = ThreadPoolStats(
                total_tasks=self.total_tasks,
                completed_tasks=self.completed_tasks,
                failed_tasks=self.failed_tasks,
                queue_size=queue_size,
                active_threads=active_threads,
                avg_task_time=avg_task_time,
                pool_utilization=pool_utilization
            )
        return stats

    def get_queue_length(self) -> int:
        """获取任务队列长度"""
        with self._lock:
            return self._work_queue.qsize()

    def _ensure_min_workers(self):
        """确保至少有 min_workers 个线程处于活跃状态"""
        with self._lock:
            current_threads = len([t for t in self._threads if t.is_alive()])
            threads_to_create = self.min_workers - current_threads

            if threads_to_create > 0:
                # 创建一些空闲的工作线程
                for _ in range(threads_to_create):
                    # 提交一个空任务来初始化线程
                    future = super().submit(lambda: None)
                    future.result()  # 等待线程创建完成
                logger.info(f"Created {threads_to_create} additional threads to maintain minimum of {self.min_workers}")


class ThreadPoolManager:
    """线程池管理器"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            from .task_manager import TaskType
            # 存储 worker 实例的映射
            self.workers: Dict[str, Any] = {}

            # 使用 MonitoredThreadPoolExecutor 初始化线程池
            self.pools: Dict[str, MonitoredThreadPoolExecutor] = {
                TaskType.FILE.value: MonitoredThreadPoolExecutor(
                    name='file_processing',
                    min_workers=flowsettings.FILE_WORKER_THREAD_MIN,
                    max_workers=flowsettings.FILE_WORKER_THREAD_MAX,
                    thread_name_prefix='file_worker'
                ),
                TaskType.CHAT.value: MonitoredThreadPoolExecutor(
                    name='chat',
                    min_workers=flowsettings.CHAT_WORKER_THREAD_MIN,
                    max_workers=flowsettings.CHAT_WORKER_THREAD_MAX,
                    thread_name_prefix='chat_worker'
                )
            }
            # 存储任务ID到Future的映射
            self.task_futures: Dict[str, Future] = {}
            self._monitor_thread = None
            self._should_monitor = False
            self.initialized = True
            logger.info("ThreadPoolManager initialized with MonitoredThreadPoolExecutor")

    def register_worker(self, task_type: str, worker_func):
        """注册工作器实例"""
        self.workers[task_type] = worker_func
        logger.info(f"Registered worker for task type: {task_type}")

    def get_pool(self, task_type: str) -> MonitoredThreadPoolExecutor:
        """获取指定类型的线程池"""
        if task_type not in self.pools:
            raise ValueError(f"Unknown task type: {task_type}")
        return self.pools[task_type]

    def submit_task(
            self,
            task_type: str,
            callback: Callable,
            task_id: str,
            task_data: Dict[str, Any]
    ) -> Future:
        """
        提交任务到线程池

        Args:
            task_type: 任务类型
            callback: 任务完成后的回调函数
            task_id: 任务ID
            task_data: 任务数据

        Returns:
            Future: 任务的Future对象
        """
        pool = self.get_pool(task_type)
        worker_func = self.workers.get(task_type)

        if not worker_func:
            raise ValueError(f"No worker registered for task type: {task_type}")

        # 创建任务包装函数
        async def task_wrapper():
            try:
                # 调用注册的worker函数 (如 FileWorker.process_file_task)
                result = await worker_func(task_id, task_data)
                callback(task_id, True, result=result)
                return result
            except Exception as e:
                logger.error(f"Task {task_id} failed: {e}")
                callback(task_id, False, error=str(e))
                raise

        # 使用 asyncio.run 包装异步任务
        def run_async_task():
            asyncio.run(task_wrapper())

        # 提交任务到线程池
        future = pool.submit(run_async_task)
        self.task_futures[task_id] = future
        return future

    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        future = self.task_futures.get(task_id)
        if future and not future.done():
            cancelled = future.cancel()
            if cancelled:
                del self.task_futures[task_id]
            return cancelled
        return False

    def shutdown(self, wait: bool = True):
        """关闭所有线程池"""
        for pool in self.pools.values():
            pool.shutdown(wait=wait)

    def get_system_resources(self) -> dict:
        """获取系统资源使用情况"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'load_avg': psutil.getloadavg()
        }

    def get_pools_stats(self) -> Dict[str, Dict]:
        """获取所有线程池的统计信息"""
        stats_dict = {}
        for pool_type, pool in self.pools.items():
            if isinstance(pool, MonitoredThreadPoolExecutor):
                stats = pool.get_stats().to_dict()
                stats_dict[pool_type] = stats
        return stats_dict

    def start_queue_monitor(self, interval: int = 60):
        """
        启动队列监控线程。
        :param interval: 监控间隔时间（秒）
        """

        if self._monitor_thread is None or not self._monitor_thread.is_alive():
            self._should_monitor = True
            self._monitor_thread = threading.Thread(
                target=self.monitor_queues,
                args=(interval,),
                daemon=True
            )
            self._monitor_thread.start()

    def monitor_queues(self, interval: int):
        """简化为只监控状态，不进行调整"""
        while self._should_monitor:
            try:
                stats = self.get_pools_stats()
                resources = self.get_system_resources()

                for pool_type, pool_stats in stats.items():
                    logger.info(
                        f"Pool {pool_type} status: "
                        f"active_threads={pool_stats['active_threads']}, "
                        f"queue_size={pool_stats['queue_size']}, "
                        f"completed_tasks={pool_stats['completed_tasks']}, "
                        f"failed_tasks={pool_stats['failed_tasks']}, "
                        f"avg_task_time={pool_stats['avg_task_time']:.2f}s"
                    )

                    # 记录系统资源使用情况
                    logger.debug(
                        f"System resources: CPU={resources['cpu_percent']}%, "
                        f"Memory={resources['memory_percent']}%"
                    )
            except Exception as e:
                logger.error(f"Error in queue monitor: {e}")

            time.sleep(interval)


# 全局线程池管理器实例
_thread_pool_manager = None


def get_thread_pool_manager() -> ThreadPoolManager:
    """获取线程池管理器实例"""
    global _thread_pool_manager
    if _thread_pool_manager is None:
        _thread_pool_manager = ThreadPoolManager()
    return _thread_pool_manager
