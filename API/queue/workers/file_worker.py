import asyncio
import logging
import time
from fastapi import HTTPException
from ..task_manager import TaskManager

logger = logging.getLogger(__name__)


class FileWorker:
    def __init__(self, task_manager: TaskManager):
        self.task_manager = task_manager
        self.index_manager = None  # 将由App注入
        logger.info("FileWorker initialized with TaskManager")

    async def process_file_task(self, task_id: str, payload: dict):
        """
        处理单个文件任务
        Args:
            task_id (str): 任务唯一 ID
            payload (dict): 任务的载荷数据，包含 file_paths, reindex, settings 等
        """
        logger.info(f"FileWorker - Start processing task {task_id} with payload: {payload}")
        start_time = time.time()

        # 1. 获取文件路径和其他参数
        file_paths = payload.get("file_paths", [])
        reindex = payload.get("reindex", False)
        settings = payload.get("settings", {})
        user_id = payload.get("user_id")

        logger.debug(
            f"Task {task_id} parameters - file_paths: {file_paths}, reindex: {reindex}, settings: {settings}")

        # 2. 为任务创建专属的索引实例
        task_index = await self.index_manager.create_task_index(
            name=f"task_{task_id}",
            config=settings
        )
        
        if not task_index:
            raise ValueError("Failed to create task index")

        # 3. 获取任务专属的handler
        handler = task_index._file_handler

        # 4. 验证文件
        errors = handler.validate(file_paths)
        if errors:
            logger.warning(f"Validation errors in task {task_id}: {errors}")
            raise HTTPException(status_code=400, detail=errors)

        # 5. 处理文件上传和索引
        file_ids = await handler.handle_upload_and_index(
            files=file_paths,
            reindex=reindex,
            settings=settings,
            task_id=task_id
        )

        # 6. 等待向量数据库写入完成

        end_time = time.time()
        logger.info(f"FileWorker - Task {task_id} completed successfully with file_ids: {file_paths}, "
                   f"total time (including vector DB write): {end_time - start_time}")
        return {"file_ids": file_ids}
